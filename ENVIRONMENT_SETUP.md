# Environment Setup Guide

## Overview
This application uses environment variables to store sensitive configuration data like API keys, database URLs, and authentication settings. These files are excluded from version control for security reasons.

## Quick Setup

### 1. Backend Environment Setup
```bash
# Copy the template file
cp app/backend/.env.template app/backend/.env

# Edit the file with your actual values
nano app/backend/.env  # or use your preferred editor
```

### 2. Frontend Environment Setup
```bash
# Copy the template file
cp app/frontend/.env.template app/frontend/.env

# Edit the file with your actual values
nano app/frontend/.env  # or use your preferred editor
```

## Required Configuration

### Backend (.env)
- **OPENAI_API_KEY**: Your OpenAI API key from https://platform.openai.com/api-keys
- **DATABASE_API_URL**: URL for the main database service (default: http://database:8001)
- **JD_DATABASE_API**: URL for job description database (default: http://jddatabase:8009)
- **AR_DATABASE_API**: URL for archive database (default: http://ardatabase:8007)

### Frontend (.env)
- **REACT_APP_BACKEND_API**: Backend API authentication key
- **REACT_APP_AZURE_CLIENT_ID**: Azure AD application client ID
- **REACT_APP_AZURE_AUTHORITY**: Azure AD authority URL

## Security Notes

⚠️ **IMPORTANT**: Never commit .env files to version control!

- .env files contain sensitive information like API keys and secrets
- Template files (.env.template) are safe to commit and show the required structure
- The .gitignore file is configured to exclude all .env files automatically

## Docker Development

When using Docker, the environment variables are automatically loaded from the .env files in each service directory.

## Production Deployment

For production:
1. Use environment variables set directly in your deployment platform
2. Never use .env files in production containers
3. Use secure secret management services (AWS Secrets Manager, Azure Key Vault, etc.)

## Troubleshooting

If you encounter issues:
1. Verify all required environment variables are set
2. Check that .env files exist in both app/backend/ and app/frontend/
3. Ensure API keys are valid and have proper permissions
4. Restart Docker containers after changing .env files
