version: '3'

# YOU MUST define the external network "interviewGPT_network" manually on the command line so the 2 docker-compose files can communicate
# Reason being that we can update the frontend and backend without worrying about changing the db container
# If the db container is re-built, the data in the database will be lost
# We will need to implement a db backup at some point (cloud storage or another methodology)
networks:
  interviewGPT_network:
    external: true

services:
  interviewGPT:
    image: interviewback
    build:
      context: ../../app/backend
    ports:
      - "8002:8002"
    networks:
      - interviewGPT_network

  react:
    image: interviewui
    build:
      context: ../../app/frontend
    ports:
      - "3000:3000"
    depends_on:
      - interviewGPT
    networks:
      - interviewGPT_network