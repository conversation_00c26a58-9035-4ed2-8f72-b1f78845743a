# Production Configuration
# This file contains production-specific settings

# Application Settings
app:
  name: "InterviewBot-V2"
  version: "2.0.0"
  environment: "production"
  debug: false

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8002
  workers: 8
  timeout: 1600
  max_content_length: 5368709120  # 5GB

# Database Configuration
database:
  main:
    url: "sqlite:///candidateDB"
    host: "database"
    port: 8001
  job_descriptions:
    url: "sqlite:///jdDB"
    host: "jddatabase"
    port: 8009
  archive:
    url: "sqlite:///archiveDB"
    host: "ardatabase"
    port: 8007

# External APIs
apis:
  openai:
    base_url: "https://api.openai.com/v1"
    model: "gpt-4"
    max_tokens: 2000
    temperature: 0.8
  gemini:
    base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
    model: "gemini-2.0-flash"
  proxycurl:
    base_url: "https://nubela.co/proxycurl/api/v2"
    endpoint: "/linkedin"

# Security Configuration
security:
  cors:
    origins:
      - "https://interviews.creospan.com"
  custom_header:
    key: "my_custom_header_key"
    value: "DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC"

# Azure AD Configuration
azure:
  client_id: "04efb143-adb9-43eb-ab40-e8bfca657adb"
  authority: "https://login.microsoftonline.com/creospan.com"
  redirect_uri: "https://interviews.creospan.com"
  post_logout_redirect_uri: "https://interviews.creospan.com"
  scopes:
    - "user.read"

# File Upload Configuration
uploads:
  max_size: 5368709120  # 5GB
  allowed_extensions:
    - ".pdf"
    - ".docx"
    - ".doc"
    - ".txt"
  upload_folder: "./uploads"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/app.log"

# Docker Network Configuration
docker:
  network: "interviewGPT_network"
  services:
    frontend:
      name: "react"
      port: 3000
    backend:
      name: "interviewGPT"
      port: 8000
    database:
      name: "database"
      port: 8003
    jd_database:
      name: "jddatabase"
      port: 8004
    archive_database:
      name: "ardatabase"
      port: 8005

# SSL Configuration (if using HTTPS)
ssl:
  enabled: true
  cert_file: "/etc/ssl/certs/interviews.creospan.com.crt"
  key_file: "/etc/ssl/private/interviews.creospan.com.key"

# Performance Configuration
performance:
  cache:
    enabled: true
    type: "redis"
    host: "redis"
    port: 6379
  rate_limiting:
    enabled: true
    requests_per_minute: 60
