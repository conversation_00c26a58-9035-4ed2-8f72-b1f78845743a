# nginx is responsible for 2 processes:
# 1) serving the static react files for production mode
# 2) handling the CORS error when connected to the backend server
# Because the process of generating an interview is roughly 5 minutes and it requires 2 potentially large files
# the timeout and the client max body size are manually increased
server {
  listen 3000;
  listen [::]:3000;
  server_name react;
  client_max_body_size 5000M;

  location / {
    root /usr/share/nginx/html;  # Serve React files
    try_files $uri /index.html;   # Redirect to index.html for SPA
    add_header Cache-Control "no-cache, no-store, must-revalidate"; # Clear Cache for Browser
  }



  location /uploadQA {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    proxy_read_timeout 600; 
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    # Because the frontend docker container and the backend docker container are on the same docker network,
    # the backend service 'interviewGPT' (defined in the docker-compose file) can be utilized directly here.
    # Port 8000 because <PERSON><PERSON> runs the flask app (which has port 8002) on port 8000
    proxy_pass http://interviewGPT:8000;
  }

  location /regenerateBehavioral {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    proxy_read_timeout 600; 
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    # Because the frontend docker container and the backend docker container are on the same docker network,
    # the backend service 'interviewGPT' (defined in the docker-compose file) can be utilized directly here.
    # Port 8000 because gunicorn runs the flask app (which has port 8002) on port 8000
    proxy_pass http://interviewGPT:8000;
  }

  location /regenerateTechnical {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    proxy_read_timeout 600; 
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    # Because the frontend docker container and the backend docker container are on the same docker network,
    # the backend service 'interviewGPT' (defined in the docker-compose file) can be utilized directly here.
    # Port 8000 because gunicorn runs the flask app (which has port 8002) on port 8000
    proxy_pass http://interviewGPT:8000;
  }

  location /getImage {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    proxy_read_timeout 600; 
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    # Because the frontend docker container and the backend docker container are on the same docker network,
    # the backend service 'interviewGPT' (defined in the docker-compose file) can be utilized directly here.
    # Port 8000 because gunicorn runs the flask app (which has port 8002) on port 8000
    proxy_pass http://interviewGPT:8000;
  }

  location /saveCandidate {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /getAll {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /getCandidate {

    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /deleteInterview {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }
  location /saveJD {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /getAllPresets {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /getPreset {

    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /deleteJD {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /saveAR {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /getAllARPresets {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }


  location /getARPreset {

    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }

  location /deleteAR {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }





  location /assessLinkedin {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'POST';
    add_header 'Access-Control-Allow-Headers' '*';

    proxy_read_timeout 600; 
    proxy_connect_timeout 600;
    proxy_send_timeout 600;

    if ($http_my_custom_header_key != 'DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC') {
      return 403; # Forbidden
    }

    proxy_pass http://interviewGPT:8000;
  }
}