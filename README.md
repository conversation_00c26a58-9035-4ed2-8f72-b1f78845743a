# InterviewBot-V2

InterviewBot-V2 is an AI-powered interview question generation system that creates personalized technical and behavioral interview questions based on candidate resumes and job descriptions.

## 🚀 Features

- **AI-Powered Question Generation**: Uses OpenAI GPT-4 to generate relevant interview questions
- **Resume Analysis**: Extracts and analyzes candidate information from uploaded resumes
- **Job Description Matching**: Tailors questions based on specific job requirements
- **LinkedIn Integration**: Optional LinkedIn profile analysis for enhanced candidate insights
- **Multiple Question Types**: Generates both technical and behavioral interview questions
- **Question Regeneration**: Ability to regenerate questions with different parameters
- **Candidate Management**: Store and manage candidate profiles and interview data
- **Archive System**: Archive completed interviews for future reference

## 🛠 Technology Stack

### Backend
- **Python 3.9+** - Programming language
- **Flask** - Web framework
- **OpenAI API** - AI/ML inference
- **PyPDF2** - PDF text extraction
- **python-docx** - Word document processing
- **SQLite** - Database storage
- **Gunicorn** - WSGI HTTP Server

### Frontend
- **React 18** - Frontend framework
- **JavaScript/ES6+** - Programming language
- **CSS3/SCSS** - Styling
- **Nginx** - Web server (production)

### Infrastructure
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration

## 📋 Prerequisites

- Docker and Docker Compose
- OpenAI API key
- (Optional) Google Gemini API key
- (Optional) ProxyCurl API key for LinkedIn integration

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/InterviewBot-V2.git
cd InterviewBot-V2
```

### 2. Set Up Environment Variables
```bash
# Backend environment
cp app/backend/.env.template app/backend/.env
# Edit app/backend/.env and add your API keys

# Frontend environment  
cp app/frontend/.env.template app/frontend/.env
# Edit app/frontend/.env if needed
```

### 3. Start the Application
```bash
# Using the setup script (recommended)
./scripts/setup.sh

# Or manually
docker network create interviewGPT_network
cd db && docker compose up -d && cd ..
cd config/docker && docker compose up --build
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8002

## ⚙️ Configuration

### Environment Variables

#### Backend (.env)
```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
GEMINI_API_KEY=your_gemini_api_key_here
PROXYCURL_API_KEY=your_proxycurl_api_key_here

# Database URLs (for local development)
DATABASE_API_URL=http://localhost:8001
JD_DATABASE_API=http://localhost:8009
AR_DATABASE_API=http://localhost:8007
```

#### Frontend (.env)
```env
REACT_APP_BACKEND_API=your_backend_api_key_here
REACT_APP_API_BASE_URL=http://localhost:8002
REACT_APP_ENV=development
```

## 📖 Usage

1. **Upload Files**: Upload a candidate's resume and the job description
2. **Generate Questions**: Click "Generate Questions" to create AI-powered interview questions
3. **Review Questions**: Review the generated technical and behavioral questions
4. **Regenerate**: Use the regenerate feature to create alternative questions
5. **Save Interview**: Save the interview data for future reference
6. **Manage Candidates**: Use the dashboard to manage candidate profiles and interviews

## 🏗 Project Structure

```
InterviewBot-V2/
├── app/
│   ├── backend/
│   │   ├── api/                 # API route modules
│   │   ├── config/              # Configuration files
│   │   ├── services/            # Business logic services
│   │   ├── models/              # Data models
│   │   ├── main.py              # Flask application entry point
│   │   ├── llm_inference.py     # LLM integration
│   │   ├── utils.py             # Utility functions
│   │   └── requirements.txt     # Python dependencies
│   └── frontend/
│       ├── src/
│       │   ├── components/      # React components
│       │   ├── pages/           # Page components
│       │   ├── modals/          # Modal components
│       │   ├── services/        # API services
│       │   ├── utils/           # Utility functions
│       │   ├── constants/       # Constants and configuration
│       │   └── assets/          # Static assets
│       └── package.json         # Node.js dependencies
├── config/                      # Centralized configuration
│   ├── docker/                  # Docker configurations
│   ├── nginx/                   # Nginx configurations
│   ├── development.yml          # Development config
│   └── production.yml           # Production config
├── db/                          # Database services
│   ├── server/                  # Main database
│   ├── desc/                    # Job description database
│   └── archive/                 # Archive database
├── scripts/                     # Setup and utility scripts
├── logs/                        # Application logs
└── uploads/                     # File uploads
```

## 🔧 Development

### Running in Development Mode

#### Backend Development
```bash
cd app/backend
pip install -r requirements.txt
python main.py
```

#### Frontend Development
```bash
cd app/frontend
npm install
npm start
```

### Database Services

The application uses separate database services:
- **Main Database** (port 8001): Candidate data
- **Job Description Database** (port 8009): Job descriptions  
- **Archive Database** (port 8007): Archived interviews

Start database services:
```bash
cd db
docker compose up
```

## 📚 API Documentation

### Endpoints

#### Candidate Management
- `GET /getAll` - Get all candidates
- `POST /getCandidate` - Get specific candidate
- `POST /addCandidate` - Add new candidate
- `POST /updateCandidate` - Update candidate
- `POST /deleteCandidate` - Delete candidate

#### Interview Questions
- `POST /generateQuestions` - Generate interview questions
- `POST /regenerateTechnical` - Regenerate technical questions
- `POST /regenerateBehavioral` - Regenerate behavioral questions

#### Job Descriptions
- `GET /getAllJD` - Get all job descriptions
- `POST /getJD` - Get specific job description
- `POST /addJD` - Add job description
- `POST /updateJD` - Update job description
- `POST /deleteJD` - Delete job description

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

## 📝 Changelog

### Version 2.0.0
- ✅ Restructured codebase with modular architecture
- ✅ Improved separation of concerns
- ✅ Added comprehensive configuration management
- ✅ Enhanced API organization
- ✅ Updated documentation
- ✅ Cleaned up unused dependencies and files
- ✅ Centralized configuration files
- ✅ Improved frontend structure with services and constants
