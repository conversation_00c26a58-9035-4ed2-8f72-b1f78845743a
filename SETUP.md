# InterviewBot-V2 Setup Guide

This guide provides detailed instructions for setting up and running the InterviewBot-V2 application.

## 📋 Prerequisites

Before setting up the application, ensure you have the following installed:

- **Docker** (version 20.10 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Git** (for cloning the repository)

### API Keys Required

You'll need to obtain the following API keys:

1. **OpenAI API Key**
   - Visit: https://platform.openai.com/api-keys
   - Create an account and generate an API key
   - Used for GPT-4 text generation

2. **ProxyCurl API Key**
   - Visit: https://nubela.co/proxycurl/
   - Sign up for an account and get an API key
   - Used for LinkedIn profile data scraping

3. **Google Gemini API Key** (Optional)
   - Visit: https://aistudio.google.com/app/apikey
   - Alternative LLM option to OpenAI

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd InterviewBot-V2
   ```

2. **Run the setup script:**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **Update environment variables:**
   ```bash
   # Edit backend environment file
   nano app/backend/.env
   
   # Add your actual API keys:
   OPENAI_API_KEY=your_actual_openai_api_key_here
   PROXYCURL_API_KEY=your_actual_proxycurl_api_key_here
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   ```

4. **Start the application:**
   ```bash
   ./scripts/start.sh
   ```

5. **Access the application:**
   - Open your browser and go to: http://localhost:3000
   - Sign in with your Creospan email credentials

### Option 2: Manual Setup

1. **Clone and navigate to the repository:**
   ```bash
   git clone <repository-url>
   cd InterviewBot-V2
   ```

2. **Create environment files:**
   ```bash
   # Backend environment file
   cp app/backend/.env.template app/backend/.env
   
   # Frontend environment file
   cp app/frontend/.env.template app/frontend/.env
   ```

3. **Update API keys in backend .env file:**
   ```bash
   nano app/backend/.env
   ```

4. **Create Docker network:**
   ```bash
   docker network create interviewGPT_network
   ```

5. **Build database containers:**
   ```bash
   # Main database
   docker build -t database db/server/.
   
   # Job description database
   cd db/desc
   docker build -t jddatabase .
   cd ../..
   
   # Archive database
   cd db/archive
   docker build -t ardatabase .
   cd ../..
   ```

6. **Start database services:**
   ```bash
   cd db/
   docker compose up -d
   cd ..
   ```

7. **Build and start application:**
   ```bash
   cd app/
   docker compose build
   docker compose up
   ```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```bash
# Required API Keys
OPENAI_API_KEY=your_openai_api_key
PROXYCURL_API_KEY=your_proxycurl_api_key
GEMINI_API_KEY=your_gemini_api_key

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
```

#### Frontend (.env)
```bash
# Backend API Configuration
REACT_APP_BACKEND_API=DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC

# Environment
REACT_APP_ENV=development

# Azure AD Configuration
REACT_APP_AZURE_CLIENT_ID=04efb143-adb9-43eb-ab40-e8bfca657adb
REACT_APP_AZURE_AUTHORITY=https://login.microsoftonline.com/creospan.com
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_POST_LOGOUT_REDIRECT_URI=http://localhost:3000
```

### Port Configuration

The application uses the following ports:

- **Frontend**: 3000
- **Backend**: 8002 (internal), 8000 (Gunicorn)
- **Main Database**: 8003
- **Job Description Database**: 8004
- **Archive Database**: 8005

## 🎯 Usage

### Starting the Application

```bash
# Start in foreground (see logs)
./scripts/start.sh

# Start in background (detached mode)
./scripts/start.sh -d
```

### Stopping the Application

```bash
# Stop application services only
./scripts/stop.sh

# Stop all services (including databases)
./scripts/stop.sh --all

# Stop and clean up containers/images
./scripts/stop.sh --clean

# Complete reset (removes all data)
./scripts/stop.sh --reset
```

### Accessing the Application

1. Open your browser and navigate to: http://localhost:3000
2. Sign in with your Creospan email and password
3. You'll see two main options:
   - **View Past Interviews**: Browse previously generated interviews
   - **Generate New Interview Notes**: Create new interview questions

## 🔍 Troubleshooting

### Common Issues

1. **Docker network error:**
   ```bash
   docker network create interviewGPT_network
   ```

2. **Permission denied on scripts:**
   ```bash
   chmod +x scripts/*.sh
   ```

3. **Port already in use:**
   ```bash
   # Check what's using the port
   lsof -i :3000
   
   # Kill the process or change the port in docker-compose.yml
   ```

4. **API key errors:**
   - Verify your API keys are correct in `app/backend/.env`
   - Ensure there are no extra spaces or quotes around the keys

5. **Database connection issues:**
   ```bash
   # Restart database services
   cd db/
   docker compose restart
   cd ..
   ```

### Viewing Logs

```bash
# View all logs
docker compose -f app/docker-compose.yml logs -f

# View specific service logs
docker compose -f app/docker-compose.yml logs -f react
docker compose -f app/docker-compose.yml logs -f interviewGPT
```

### Checking Service Status

```bash
# View running containers
docker ps

# View service status
docker compose -f app/docker-compose.yml ps
docker compose -f db/docker-compose.yml ps
```

## 🔄 Development

### Making Changes

1. **Backend changes**: Edit files in `app/backend/`
2. **Frontend changes**: Edit files in `app/frontend/src/`
3. **Database changes**: Edit files in `db/` (be careful with data loss)

### Rebuilding Containers

```bash
# Rebuild specific service
docker compose -f app/docker-compose.yml build react
docker compose -f app/docker-compose.yml build interviewGPT

# Rebuild all services
cd app/
docker compose build --no-cache
```

## 📁 File Structure

```
InterviewBot-V2/
├── app/
│   ├── backend/
│   │   ├── .env                 # Backend environment variables
│   │   ├── .env.template        # Template for backend .env
│   │   ├── requirements.txt     # Python dependencies
│   │   └── ...
│   ├── frontend/
│   │   ├── .env                 # Frontend environment variables
│   │   ├── .env.template        # Template for frontend .env
│   │   ├── package.json         # Node.js dependencies
│   │   └── ...
│   └── docker-compose.yml       # App services configuration
├── db/
│   ├── server/                  # Main database
│   ├── desc/                    # Job description database
│   ├── archive/                 # Archive database
│   └── docker-compose.yml       # Database services configuration
├── config/
│   ├── development.yml          # Development configuration
│   └── production.yml           # Production configuration
├── scripts/
│   ├── setup.sh                 # Setup script
│   ├── start.sh                 # Start script
│   └── stop.sh                  # Stop script
├── .env.example                 # Example environment variables
├── SETUP.md                     # This file
└── README.md                    # Main documentation
```

## 🚀 Production Deployment

For production deployment, see the deployment section in the main README.md file.

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Review the logs for error messages
3. Ensure all prerequisites are installed
4. Verify your API keys are correct
5. Contact the development team for assistance

## 📝 Notes

- The application requires internet access for API calls
- Large file uploads (up to 5GB) are supported
- The interview generation process takes approximately 5 minutes
- All data is stored locally in SQLite databases
- Microsoft Azure AD authentication is required for access
