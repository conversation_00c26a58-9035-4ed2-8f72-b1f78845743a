# InterviewBot-V2 .gitignore

# =============================================================================
# ENVIRONMENT VARIABLES & SECRETS
# =============================================================================
# Environment files (contain sensitive data like API keys)
.env
.env.*
!.env.example
!.env.template
!.env.sample

# Specific environment files
app/backend/.env
app/frontend/.env
app/backend/.env.*
app/frontend/.env.*
!app/backend/.env.template
!app/frontend/.env.template
!app/backend/.env.example
!app/frontend/.env.example

# Any .env file in any subdirectory
**/.env
**/.env.*
!**/.env.template
!**/.env.example
!**/.env.sample

# API Keys and secrets
secrets/
*.key
*.pem
*.p12
*.pfx

# =============================================================================
# PYTHON
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# NODE.JS / REACT
# =============================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# DOCKER
# =============================================================================
# Docker override files (may contain sensitive data)
docker-compose.override.yml
docker-compose.*.yml
!docker-compose.yml

# =============================================================================
# DATABASES
# =============================================================================
# SQLite databases
*.db
*.sqlite
*.sqlite3
candidateDB
jdDB
archiveDB

# Database data directories
db/data/
db/jd_data/
db/archive_data/

# =============================================================================
# LOGS & UPLOADS
# =============================================================================
# Log files
logs/
*.log
log/

# Upload directories
uploads/
upload/
tmp/

# =============================================================================
# IDE & EDITORS
# =============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OPERATING SYSTEM
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================
# Backup files
*.bak
*.backup
*.old

# Test files
test_*.txt
test_*.pdf
test_*.docx

# Temporary processing files
/tmp/
temp_*

# Generated documentation
docs/build/

# Coverage reports
coverage/
.coverage

# Deployment files
deploy/
deployment/

# Local configuration overrides
local_config.py
local_settings.py

# =============================================================================
# SECURITY
# =============================================================================
# Certificate files
*.crt
*.cer
*.der
*.p7b
*.p7c
*.p7r
*.p7s

# SSH keys
id_rsa
id_rsa.pub
id_dsa
id_dsa.pub
id_ecdsa
id_ecdsa.pub
id_ed25519
id_ed25519.pub

# GPG keys
*.gpg
*.asc
