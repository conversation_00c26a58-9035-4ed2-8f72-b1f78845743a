#!/bin/bash

# InterviewBot-V2 Setup Script
# This script sets up the development environment for InterviewBot-V2

set -e

echo "🚀 Setting up InterviewBot-V2 Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker is installed"
}

# Check if Docker Compose is installed
check_docker_compose() {
    print_status "Checking Docker Compose installation..."
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose is installed"
}

# Create environment files from templates
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend .env file
    if [ ! -f "app/backend/.env" ]; then
        if [ -f "app/backend/.env.template" ]; then
            cp app/backend/.env.template app/backend/.env
            print_success "Created app/backend/.env from template"
            print_warning "Please update the API keys in app/backend/.env"
        else
            print_error "Template file app/backend/.env.template not found"
        fi
    else
        print_warning "app/backend/.env already exists, skipping..."
    fi
    
    # Frontend .env file
    if [ ! -f "app/frontend/.env" ]; then
        if [ -f "app/frontend/.env.template" ]; then
            cp app/frontend/.env.template app/frontend/.env
            print_success "Created app/frontend/.env from template"
        else
            print_error "Template file app/frontend/.env.template not found"
        fi
    else
        print_warning "app/frontend/.env already exists, skipping..."
    fi
}

# Create Docker network
create_docker_network() {
    print_status "Creating Docker network..."
    if ! docker network ls | grep -q "interviewGPT_network"; then
        docker network create interviewGPT_network
        print_success "Created interviewGPT_network"
    else
        print_warning "interviewGPT_network already exists, skipping..."
    fi
}

# Build database containers
build_database_containers() {
    print_status "Building database containers..."
    
    # Main database
    print_status "Building main database container..."
    docker build -t database db/server/.
    print_success "Built main database container"
    
    # Job description database
    print_status "Building job description database container..."
    cd db/desc
    docker build -t jddatabase .
    cd ../..
    print_success "Built job description database container"
    
    # Archive database
    print_status "Building archive database container..."
    cd db/archive
    docker build -t ardatabase .
    cd ../..
    print_success "Built archive database container"
}

# Start database services
start_database_services() {
    print_status "Starting database services..."
    cd db/
    docker compose up -d
    cd ..
    print_success "Database services started"
}

# Build application containers
build_app_containers() {
    print_status "Building application containers..."
    cd config/docker/
    docker compose build
    cd ../..
    print_success "Application containers built"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p logs
    mkdir -p uploads
    mkdir -p db/data
    mkdir -p db/jd_data
    mkdir -p db/archive_data
    print_success "Directories created"
}

# Main setup function
main() {
    echo "🎯 InterviewBot-V2 Setup Script"
    echo "================================"
    
    check_docker
    check_docker_compose
    create_directories
    setup_env_files
    create_docker_network
    build_database_containers
    start_database_services
    build_app_containers
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Update API keys in app/backend/.env"
    echo "2. Update configuration in app/frontend/.env if needed"
    echo "3. Run 'cd app && docker compose up' to start the application"
    echo "4. Access the application at http://localhost:3000"
    echo ""
    echo "For more information, see the README.md file."
}

# Run main function
main "$@"
