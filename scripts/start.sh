#!/bin/bash

# InterviewBot-V2 Start Script
# This script starts the InterviewBot-V2 application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment files exist
check_env_files() {
    print_status "Checking environment files..."
    
    if [ ! -f "app/backend/.env" ]; then
        print_error "app/backend/.env not found. Please run setup.sh first."
        exit 1
    fi
    
    if [ ! -f "app/frontend/.env" ]; then
        print_error "app/frontend/.env not found. Please run setup.sh first."
        exit 1
    fi
    
    print_success "Environment files found"
}

# Check if Docker network exists
check_docker_network() {
    print_status "Checking Docker network..."
    if ! docker network ls | grep -q "interviewGPT_network"; then
        print_error "interviewGPT_network not found. Please run setup.sh first."
        exit 1
    fi
    print_success "Docker network found"
}

# Check if database containers are running
check_database_services() {
    print_status "Checking database services..."
    
    if ! docker ps | grep -q "database"; then
        print_warning "Database services not running. Starting them..."
        cd db/
        docker compose up -d
        cd ..
        sleep 5
    fi
    
    print_success "Database services are running"
}

# Start application services
start_app_services() {
    print_status "Starting application services..."
    cd config/docker/

    # Check if we should run in detached mode
    if [ "$1" = "-d" ] || [ "$1" = "--detach" ]; then
        docker compose up -d
        print_success "Application services started in detached mode"
        print_status "Access the application at http://localhost:3000"
    else
        print_status "Starting application services in foreground mode..."
        print_status "Press Ctrl+C to stop the services"
        print_status "Access the application at http://localhost:3000"
        docker compose up
    fi
    
    cd ..
}

# Show service status
show_status() {
    print_status "Service Status:"
    echo ""
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(database|jddatabase|ardatabase|interviewGPT|react)"
    echo ""
}

# Main function
main() {
    echo "🚀 Starting InterviewBot-V2..."
    echo "=============================="
    
    check_env_files
    check_docker_network
    check_database_services
    start_app_services "$1"
    
    if [ "$1" = "-d" ] || [ "$1" = "--detach" ]; then
        echo ""
        show_status
        echo "🎉 InterviewBot-V2 is now running!"
        echo ""
        echo "Useful commands:"
        echo "  - View logs: docker compose -f app/docker-compose.yml logs -f"
        echo "  - Stop services: docker compose -f app/docker-compose.yml down"
        echo "  - Restart services: docker compose -f app/docker-compose.yml restart"
        echo ""
    fi
}

# Show help
show_help() {
    echo "InterviewBot-V2 Start Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -d, --detach    Run services in detached mode"
    echo "  -h, --help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              Start services in foreground mode"
    echo "  $0 -d           Start services in detached mode"
}

# Parse command line arguments
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
