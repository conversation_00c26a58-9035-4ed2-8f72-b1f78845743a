#!/bin/bash

# InterviewBot-V2 Stop Script
# This script stops the InterviewBot-V2 application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop application services
stop_app_services() {
    print_status "Stopping application services..."
    cd app/
    docker compose down
    cd ..
    print_success "Application services stopped"
}

# Stop database services
stop_database_services() {
    print_status "Stopping database services..."
    cd db/
    docker compose down
    cd ..
    print_success "Database services stopped"
}

# Remove containers (optional)
remove_containers() {
    print_status "Removing containers..."
    cd app/
    docker compose down --remove-orphans
    cd ../db/
    docker compose down --remove-orphans
    cd ..
    print_success "Containers removed"
}

# Remove volumes (optional)
remove_volumes() {
    print_warning "This will remove all data stored in Docker volumes!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Removing volumes..."
        cd app/
        docker compose down -v
        cd ../db/
        docker compose down -v
        cd ..
        print_success "Volumes removed"
    else
        print_status "Volume removal cancelled"
    fi
}

# Clean up Docker images (optional)
cleanup_images() {
    print_status "Cleaning up Docker images..."
    
    # Remove dangling images
    if [ "$(docker images -f "dangling=true" -q)" ]; then
        docker rmi $(docker images -f "dangling=true" -q)
        print_success "Removed dangling images"
    else
        print_status "No dangling images to remove"
    fi
    
    # Remove unused images
    read -p "Remove unused Docker images? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker image prune -f
        print_success "Removed unused images"
    fi
}

# Show service status
show_status() {
    print_status "Current service status:"
    echo ""
    if docker ps | grep -E "(database|jddatabase|ardatabase|interviewGPT|react)" > /dev/null; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(database|jddatabase|ardatabase|interviewGPT|react)"
    else
        print_status "No InterviewBot services are currently running"
    fi
    echo ""
}

# Main function
main() {
    echo "🛑 Stopping InterviewBot-V2..."
    echo "=============================="
    
    case "$1" in
        --all)
            stop_app_services
            stop_database_services
            ;;
        --clean)
            stop_app_services
            stop_database_services
            remove_containers
            cleanup_images
            ;;
        --reset)
            stop_app_services
            stop_database_services
            remove_containers
            remove_volumes
            cleanup_images
            ;;
        *)
            stop_app_services
            ;;
    esac
    
    show_status
    print_success "InterviewBot-V2 stopped successfully!"
}

# Show help
show_help() {
    echo "InterviewBot-V2 Stop Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  (no options)    Stop application services only"
    echo "  --all           Stop all services (app + databases)"
    echo "  --clean         Stop all services and clean up containers/images"
    echo "  --reset         Stop all services and remove everything (including data)"
    echo "  -h, --help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              Stop application services"
    echo "  $0 --all        Stop all services"
    echo "  $0 --clean      Stop and clean up"
    echo "  $0 --reset      Complete reset (removes all data)"
}

# Parse command line arguments
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
