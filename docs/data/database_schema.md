# Database Schema Documentation

This document outlines the database structure and relationships for InterviewBot-V2's three-database architecture.

## Database Architecture Overview

```mermaid
erDiagram
    CANDIDATES {
        INTEGER id PK
        TEXT uuid
        TEXT ceipal
        TEXT date
        TEXT nickName
        TEXT name
        TEXT job
        TEXT status
        TEXT applicantNO
        TEXT jobCode
        TEXT clientName
        TEXT clientManager
        TEXT data
    }
    
    JDPRESETS {
        INTEGER id PK
        TEXT uuid
        TEXT date
        TEXT creator
        TEXT job
        TEXT desc
        TEXT clientName
        TEXT clientManager
        TEXT status
        TEXT mandQS
        TEXT data
    }
    
    ARPRESETS {
        INTEGER id PK
        TEXT uuid
        TEXT date
        TEXT creator
        TEXT job
        TEXT desc
        TEXT clientName
        TEXT clientManager
        TEXT status
        TEXT data
    }
    
    CANDIDATES ||--o{ JDPRESETS : "uses"
    CANDIDATES ||--o{ ARPRESETS : "archived_to"
```

## Database Services

### 1. Main Database (Port 8001)
- **Service Name**: `database`
- **Database File**: `candidateDB`
- **Purpose**: Stores candidate information and interview data

### 2. Job Description Database (Port 8009)
- **Service Name**: `jddatabase`
- **Database File**: `jdDB`
- **Purpose**: Manages job description templates and presets

### 3. Archive Database (Port 8007)
- **Service Name**: `ardatabase`
- **Database File**: `archiveDB`
- **Purpose**: Stores archived interviews and historical data

## Table Schemas

### Candidates Table (Main Database)

```sql
CREATE TABLE IF NOT EXISTS candidates (
    id INTEGER PRIMARY KEY,
    uuid TEXT,
    ceipal TEXT,
    date TEXT,
    nickName TEXT,
    name TEXT,
    job TEXT,
    status TEXT,
    applicantNO TEXT,
    jobCode TEXT,
    clientName TEXT,
    clientManager TEXT,
    data TEXT
);
```

**Field Descriptions:**
- `id`: Auto-incrementing primary key
- `uuid`: Unique identifier for the candidate
- `ceipal`: Ceipal system reference
- `date`: Interview date
- `nickName`: Candidate's preferred name
- `name`: Full candidate name
- `job`: Job title/position
- `status`: Interview status (Active, Completed, etc.)
- `applicantNO`: Application number
- `jobCode`: Job reference code
- `clientName`: Client company name
- `clientManager`: Client manager name
- `data`: JSON string containing full candidate data

### JDPresets Table (Job Description Database)

```sql
CREATE TABLE IF NOT EXISTS JDPresets (
    id INTEGER PRIMARY KEY,
    uuid TEXT,
    date TEXT,
    creator TEXT,
    job TEXT,
    desc TEXT,
    clientName TEXT,
    clientManager TEXT,
    status TEXT,
    mandQS TEXT,
    data TEXT
);
```

**Field Descriptions:**
- `id`: Auto-incrementing primary key
- `uuid`: Unique identifier for the job description
- `date`: Creation/modification date
- `creator`: User who created the JD
- `job`: Job title
- `desc`: Job description text
- `clientName`: Client company name
- `clientManager`: Client manager name
- `status`: JD status (Active, Draft, Archived)
- `mandQS`: Mandatory questions
- `data`: JSON string containing full JD data

### ARPresets Table (Archive Database)

```sql
CREATE TABLE IF NOT EXISTS ARPresets (
    id INTEGER PRIMARY KEY,
    uuid TEXT,
    date TEXT,
    creator TEXT,
    job TEXT,
    desc TEXT,
    clientName TEXT,
    clientManager TEXT,
    status TEXT,
    data TEXT
);
```

**Field Descriptions:**
- `id`: Auto-incrementing primary key
- `uuid`: Unique identifier for the archived item
- `date`: Archive date
- `creator`: User who created the original item
- `job`: Job title
- `desc`: Job description
- `clientName`: Client company name
- `clientManager`: Client manager name
- `status`: Archive status
- `data`: JSON string containing full archived data

## Data Flow Diagram

```mermaid
flowchart LR
    subgraph "Data Sources"
        RESUME[Resume Upload]
        JD_UPLOAD[Job Description Upload]
        LINKEDIN[LinkedIn Profile]
    end
    
    subgraph "Processing"
        EXTRACT[Text Extraction]
        AI_PROCESS[AI Processing]
        QUESTIONS[Question Generation]
    end
    
    subgraph "Storage"
        MAIN_DB[(Main Database<br/>Candidates)]
        JD_DB[(JD Database<br/>Templates)]
        AR_DB[(Archive Database<br/>Historical)]
    end
    
    RESUME --> EXTRACT
    JD_UPLOAD --> EXTRACT
    LINKEDIN --> EXTRACT
    
    EXTRACT --> AI_PROCESS
    AI_PROCESS --> QUESTIONS
    
    QUESTIONS --> MAIN_DB
    JD_UPLOAD --> JD_DB
    MAIN_DB --> AR_DB
    JD_DB --> AR_DB
    
    style MAIN_DB fill:#e8f5e8
    style JD_DB fill:#e3f2fd
    style AR_DB fill:#fff3e0
```

## API Endpoints by Database

### Main Database API (Port 8001)
- `GET /getAll` - Retrieve all candidates
- `POST /getCandidate` - Get specific candidate by UUID
- `POST /saveCandidate` - Save/update candidate data
- `POST /addCandidate` - Add new candidate
- `POST /updateCandidate` - Update existing candidate
- `POST /deleteCandidate` - Delete candidate

### Job Description Database API (Port 8009)
- `GET /getAll` - Retrieve all job descriptions
- `POST /getJD` - Get specific job description by UUID
- `POST /saveJD` - Save/update job description
- `POST /addJD` - Add new job description
- `POST /updateJD` - Update existing job description
- `POST /deleteJD` - Delete job description

### Archive Database API (Port 8007)
- `GET /getAll` - Retrieve all archived items
- `POST /getARPreset` - Get specific archived item by UUID
- `POST /saveAR` - Save/update archived item
- `POST /deleteAR` - Delete archived item

## Data Relationships

### Candidate → Job Description
- Candidates reference job descriptions through `jobCode` field
- Job descriptions can be reused across multiple candidates

### Main → Archive
- Completed interviews are archived from main database
- Archive maintains historical record of all interviews

### Job Description → Archive
- Job description templates can be archived
- Maintains version history of job descriptions

## Backup and Maintenance

### Database Files Location
- Main: `db/server/candidateDB`
- Job Descriptions: `db/desc/jdDB`
- Archive: `db/archive/archiveDB`

### Backup Strategy
- SQLite database files can be backed up by copying files
- JSON data fields contain complete records for recovery
- Archive database serves as historical backup

### Maintenance Operations
- Regular cleanup of old records
- Archive migration for completed interviews
- Index optimization for search performance
