# Frontend Components Architecture

This document outlines the React frontend component structure and relationships for InterviewBot-V2.

## Component Architecture Diagram

```mermaid
graph TB
    subgraph "Authentication Layer"
        APP[App.js<br/>MSAL Provider]
        WRAPPED[WrappedView<br/>Auth Templates]
    end
    
    subgraph "Main Application"
        INTERVIEW_GPT[InterviewGPT.js<br/>Main Router Component]
    end
    
    subgraph "Page Components"
        DASHBOARD[Dashboard.js<br/>Main Dashboard]
        CREATE_NEW[CreateNew.js<br/>New Interview Form]
        DISPLAY[DisplayInterview.js<br/>Interview Display]
        INTERVIEW_DB[Interview.js<br/>Past Interviews]
        JOB_DESC[JobDescription.js<br/>JD Management]
        MEMBERS[Members.js<br/>User Management]
        LOADING[Loading.js<br/>Loading States]
    end
    
    subgraph "Shared Components"
        THREE_DOTS[3Dots.js<br/>Action Menu]
        PROGRESS[MyProgressBar.js<br/>Progress Indicator]
        SIDEBAR[Sidebar<br/>Navigation Menu]
        MODALS[Modal Components]
    end
    
    subgraph "Services & Utils"
        API_SERVICE[apiService.js<br/>HTTP Client]
        UTILS[utils.js<br/>Helper Functions]
        CONSTANTS[API Constants]
    end
    
    subgraph "Assets & Icons"
        ICONS[Icon Components<br/>SVG Icons]
        IMAGES[Static Images]
        STYLES[CSS Styles]
    end
    
    APP --> WRAPPED
    WRAPPED --> INTERVIEW_GPT
    
    INTERVIEW_GPT --> DASHBOARD
    INTERVIEW_GPT --> CREATE_NEW
    INTERVIEW_GPT --> DISPLAY
    INTERVIEW_GPT --> INTERVIEW_DB
    INTERVIEW_GPT --> JOB_DESC
    INTERVIEW_GPT --> MEMBERS
    INTERVIEW_GPT --> LOADING
    
    DASHBOARD --> SIDEBAR
    CREATE_NEW --> SIDEBAR
    DISPLAY --> SIDEBAR
    INTERVIEW_DB --> SIDEBAR
    JOB_DESC --> SIDEBAR
    MEMBERS --> SIDEBAR
    
    DASHBOARD --> THREE_DOTS
    INTERVIEW_DB --> THREE_DOTS
    JOB_DESC --> THREE_DOTS
    
    CREATE_NEW --> PROGRESS
    LOADING --> PROGRESS
    
    INTERVIEW_GPT --> MODALS
    
    DASHBOARD --> API_SERVICE
    CREATE_NEW --> API_SERVICE
    DISPLAY --> API_SERVICE
    INTERVIEW_DB --> API_SERVICE
    JOB_DESC --> API_SERVICE
    
    API_SERVICE --> CONSTANTS
    INTERVIEW_GPT --> UTILS
    
    SIDEBAR --> ICONS
    THREE_DOTS --> ICONS
    DASHBOARD --> IMAGES
    
    style APP fill:#e1f5fe
    style INTERVIEW_GPT fill:#f3e5f5
    style API_SERVICE fill:#fff3e0
    style SIDEBAR fill:#e8f5e8
```

## Component Details

### Authentication Layer

#### App.js
- **Purpose**: Main application wrapper with MSAL authentication provider
- **Features**: 
  - Azure AD integration
  - Authentication state management
  - Route protection

#### WrappedView
- **Purpose**: Handles authenticated and unauthenticated templates
- **Features**:
  - Login redirect handling
  - Account management
  - Authentication status display

### Main Application

#### InterviewGPT.js
- **Purpose**: Central routing and state management component
- **Features**:
  - Page navigation logic
  - Global state management
  - Component orchestration
  - Loading state coordination

### Page Components

#### Dashboard.js
- **Purpose**: Main dashboard with overview and navigation
- **Features**:
  - Interview statistics
  - Quick actions
  - Recent interviews
  - Navigation menu

#### CreateNew.js
- **Purpose**: New interview creation form
- **Features**:
  - File upload (resume, job description)
  - LinkedIn profile integration
  - Form validation
  - Progress tracking

#### DisplayInterview.js
- **Purpose**: Interview questions display and management
- **Features**:
  - Question display
  - Question editing
  - Report generation
  - Download functionality

#### Interview.js
- **Purpose**: Past interviews management
- **Features**:
  - Interview listing
  - Search and filtering
  - Pagination
  - Interview loading

#### JobDescription.js
- **Purpose**: Job description template management
- **Features**:
  - JD creation and editing
  - Template library
  - Archive management
  - Admin controls

#### Members.js
- **Purpose**: User and team management (Admin only)
- **Features**:
  - User listing
  - Role management
  - Access control

#### Loading.js
- **Purpose**: Loading states and progress indication
- **Features**:
  - AI generation progress
  - Loading animations
  - Status messages

### Shared Components

#### Sidebar
- **Purpose**: Navigation sidebar with menu items
- **Features**:
  - Collapsible design
  - Icon-based navigation
  - Role-based menu items

#### 3Dots.js
- **Purpose**: Action menu component
- **Features**:
  - Dropdown actions
  - Status management
  - Archive functionality

#### MyProgressBar.js
- **Purpose**: Progress indication for long-running operations
- **Features**:
  - Visual progress tracking
  - Status updates

### Services & Utilities

#### apiService.js
- **Purpose**: Centralized API communication
- **Features**:
  - HTTP request handling
  - Error management
  - Response processing
  - Authentication headers

#### utils.js
- **Purpose**: Helper functions and utilities
- **Features**:
  - File processing
  - Data formatting
  - Common operations

## State Management

The application uses React hooks and prop drilling for state management:

- **Global State**: Managed in InterviewGPT.js
- **Local State**: Component-specific useState hooks
- **Authentication State**: Managed by MSAL provider
- **API State**: Handled by apiService.js

## Navigation Flow

1. **Authentication**: App.js → WrappedView → InterviewGPT.js
2. **Main Navigation**: InterviewGPT.js routes between page components
3. **Sidebar Navigation**: Shared across all main pages
4. **Modal Navigation**: Overlay components for specific actions

## Key Features

- **Responsive Design**: Bootstrap-based responsive layout
- **Role-Based Access**: Admin and user role differentiation
- **File Upload**: Resume and job description processing
- **Real-time Updates**: Loading states and progress tracking
- **Error Handling**: Comprehensive error management
- **Accessibility**: ARIA labels and keyboard navigation
