# InterviewBot-V2 System Architecture Documentation

This document contains comprehensive system architecture diagrams for the InterviewBot-V2 application.

## System Overview

InterviewBot-V2 is an AI-powered interview question generation system that analyzes candidate resumes and job descriptions to create tailored interview questions. The system uses a microservices architecture with separate database services and integrates with multiple AI providers.

## Architecture Diagrams

### 1. System Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        UI[React Frontend<br/>Port 3000]
        AUTH[Azure AD Authentication<br/>MSAL Provider]
    end
    
    subgraph "Application Layer"
        API[Flask Backend API<br/>Port 8002]
        CORS[CORS Middleware]
        ROUTES[API Routes]
        SERVICES[Business Services]
    end
    
    subgraph "Service Layer"
        LLM[LLM Service<br/>OpenAI GPT-4 / Gemini]
        DB_SVC[Database Service]
        FILE[File Processing Service]
    end
    
    subgraph "Database Layer"
        MAIN_DB[(Main Database<br/>Candidates<br/>Port 8001)]
        JD_DB[(Job Description DB<br/>Port 8009)]
        AR_DB[(Archive Database<br/>Port 8007)]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI API]
        GEMINI[Google Gemini API]
        PROXYCURL[ProxyCurl API<br/>LinkedIn Data]
    end
    
    subgraph "Infrastructure"
        DOCKER[Docker Network<br/>interviewGPT_network]
        UPLOADS[File Uploads<br/>Resume/JD Processing]
        LOGS[Application Logs]
    end
    
    UI --> AUTH
    AUTH --> API
    UI --> API
    API --> CORS
    API --> ROUTES
    ROUTES --> SERVICES
    SERVICES --> LLM
    SERVICES --> DB_SVC
    SERVICES --> FILE
    
    LLM --> OPENAI
    LLM --> GEMINI
    LLM --> PROXYCURL
    
    DB_SVC --> MAIN_DB
    DB_SVC --> JD_DB
    DB_SVC --> AR_DB
    
    FILE --> UPLOADS
    API --> LOGS
    
    DOCKER -.-> API
    DOCKER -.-> MAIN_DB
    DOCKER -.-> JD_DB
    DOCKER -.-> AR_DB
    
    style UI fill:#e1f5fe
    style API fill:#f3e5f5
    style LLM fill:#fff3e0
    style MAIN_DB fill:#e8f5e8
    style JD_DB fill:#e8f5e8
    style AR_DB fill:#e8f5e8
```

### 2. Application Workflow

```mermaid
flowchart TD
    START([User Login]) --> AUTH{Azure AD<br/>Authentication}
    AUTH -->|Success| DASHBOARD[Dashboard Page]
    AUTH -->|Failure| LOGIN[Login Page]
    
    DASHBOARD --> CREATE[Create New Interview]
    DASHBOARD --> VIEW[View Past Interviews]
    DASHBOARD --> JD_MGMT[Job Description Management]
    DASHBOARD --> MEMBERS[Members Management]
    
    CREATE --> UPLOAD[Upload Resume & Job Description]
    UPLOAD --> LINKEDIN{LinkedIn Profile?}
    LINKEDIN -->|Yes| FETCH_LI[Fetch LinkedIn Data<br/>via ProxyCurl]
    LINKEDIN -->|No| PROCESS
    FETCH_LI --> PROCESS[Process Documents]
    
    PROCESS --> EXTRACT[Extract Text from Files]
    EXTRACT --> GENERATE[Generate Prompts]
    GENERATE --> LLM_CALL[Call LLM Service<br/>OpenAI/Gemini]
    
    LLM_CALL --> PARSE[Parse AI Response]
    PARSE --> QUESTIONS[Generate Interview Questions]
    QUESTIONS --> DISPLAY[Display Interview]
    
    DISPLAY --> SAVE{Save Interview?}
    SAVE -->|Yes| STORE[Store in Database]
    SAVE -->|No| EDIT[Edit Questions]
    
    EDIT --> REGEN[Regenerate Questions]
    REGEN --> LLM_CALL
    
    STORE --> DOWNLOAD[Download Report]
    DOWNLOAD --> ARCHIVE[Archive Interview]
    
    VIEW --> SEARCH[Search/Filter Interviews]
    SEARCH --> LOAD_INT[Load Interview Data]
    LOAD_INT --> DISPLAY
    
    JD_MGMT --> JD_CREATE[Create Job Description]
    JD_MGMT --> JD_EDIT[Edit Job Description]
    JD_MGMT --> JD_ARCHIVE[Archive Job Description]
    
    JD_CREATE --> JD_SAVE[Save to JD Database]
    JD_EDIT --> JD_SAVE
    JD_ARCHIVE --> AR_SAVE[Save to Archive Database]
    
    MEMBERS --> USER_MGMT[User Management<br/>Admin Only]
    
    style START fill:#e8f5e8
    style AUTH fill:#fff3e0
    style LLM_CALL fill:#ffebee
    style STORE fill:#e3f2fd
    style ARCHIVE fill:#f3e5f5
```

## Key Components

### Frontend (React)
- **Port**: 3000
- **Authentication**: Azure AD with MSAL
- **Main Pages**: Dashboard, Create New, Display Interview, Past Interviews, Job Descriptions, Members
- **State Management**: React hooks and context

### Backend (Flask)
- **Port**: 8002
- **Framework**: Flask with CORS enabled
- **Services**: LLM Service, Database Service, File Processing
- **APIs**: RESTful endpoints for all operations

### Databases (SQLite)
- **Main Database** (Port 8001): Candidate data and interviews
- **Job Description Database** (Port 8009): Job description templates
- **Archive Database** (Port 8007): Archived interviews and data

### External Integrations
- **OpenAI GPT-4**: Primary LLM for question generation
- **Google Gemini**: Alternative LLM provider
- **ProxyCurl**: LinkedIn profile data extraction

### Infrastructure
- **Docker**: Containerized deployment with custom network
- **File Storage**: Local file uploads for resumes and job descriptions
- **Logging**: Application-wide logging system
