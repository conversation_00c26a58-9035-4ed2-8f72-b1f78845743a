# Deployment Architecture Documentation

This document outlines the deployment architecture, infrastructure setup, and containerization strategy for InterviewBot-V2.

## Deployment Overview

```mermaid
graph TB
    subgraph "Docker Network: interviewGPT_network"
        subgraph "Application Services"
            FRONTEND[Frontend Container<br/>React App<br/>Port 3000]
            BACKEND[Backend Container<br/>Flask API<br/>Port 8002]
        end
        
        subgraph "Database Services"
            MAIN_DB[Main Database<br/>SQLite Service<br/>Port 8001]
            JD_DB[Job Description DB<br/>SQLite Service<br/>Port 8009]
            AR_DB[Archive Database<br/>SQLite Service<br/>Port 8007]
        end
        
        subgraph "Internal Ports"
            DB_INTERNAL[Database Internal<br/>Port 8003]
            JD_INTERNAL[JD DB Internal<br/>Port 8004]
            AR_INTERNAL[AR DB Internal<br/>Port 8005]
        end
    end
    
    subgraph "External Access"
        USER[Users<br/>Browser Access]
        ADMIN[Administrators<br/>Management Access]
    end
    
    subgraph "External Services"
        OPENAI_API[OpenAI API]
        GEMINI_API[Google Gemini API]
        PROXYCURL_API[ProxyCurl API]
        AZURE_AD[Azure Active Directory]
    end
    
    subgraph "File System"
        UPLOADS[Uploads Directory<br/>File Storage]
        LOGS[Logs Directory<br/>Application Logs]
        CONFIG[Configuration Files<br/>Environment Variables]
    end
    
    USER --> FRONTEND
    ADMIN --> FRONTEND
    FRONTEND --> BACKEND
    
    BACKEND --> MAIN_DB
    BACKEND --> JD_DB
    BACKEND --> AR_DB
    
    MAIN_DB --> DB_INTERNAL
    JD_DB --> JD_INTERNAL
    AR_DB --> AR_INTERNAL
    
    BACKEND --> OPENAI_API
    BACKEND --> GEMINI_API
    BACKEND --> PROXYCURL_API
    
    FRONTEND --> AZURE_AD
    
    BACKEND --> UPLOADS
    BACKEND --> LOGS
    BACKEND --> CONFIG
    
    style FRONTEND fill:#e1f5fe
    style BACKEND fill:#f3e5f5
    style MAIN_DB fill:#e8f5e8
    style JD_DB fill:#e8f5e8
    style AR_DB fill:#e8f5e8
```

## Container Architecture

### Frontend Container
```dockerfile
# React Application
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Backend Container
```dockerfile
# Flask Application
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8002
CMD ["gunicorn", "--bind", "0.0.0.0:8002", "main:app"]
```

### Database Containers
```dockerfile
# SQLite Database Service
FROM python:3.9-slim
WORKDIR /app
COPY database.py .
COPY requirements.txt .
RUN pip install -r requirements.txt
EXPOSE 8003
CMD ["python", "database.py"]
```

## Docker Compose Configuration

### Application Services (app/docker-compose.yml)
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_BACKEND_API=http://localhost:8002
    networks:
      - interviewGPT_network
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "8002:8002"
    environment:
      - DATABASE_API_URL=http://database:8001
      - JD_DATABASE_API=http://jddatabase:8009
      - AR_DATABASE_API=http://ardatabase:8007
    volumes:
      - ../uploads:/app/uploads
      - ../logs:/app/logs
    networks:
      - interviewGPT_network
    depends_on:
      - database
      - jddatabase
      - ardatabase

networks:
  interviewGPT_network:
    external: true
```

### Database Services (db/docker-compose.yml)
```yaml
version: '3.8'
services:
  database:
    image: database
    ports:
      - "8003:8003"
    volumes:
      - ./server/candidateDB:/app/candidateDB
    networks:
      - interviewGPT_network

  jddatabase:
    image: jddatabase
    ports:
      - "8004:8004"
    volumes:
      - ./desc/jdDB:/app/jdDB
    networks:
      - interviewGPT_network

  ardatabase:
    image: ardatabase
    ports:
      - "8005:8005"
    volumes:
      - ./archive/archiveDB:/app/archiveDB
    networks:
      - interviewGPT_network

networks:
  interviewGPT_network:
    external: true
```

## Port Mapping Strategy

```mermaid
graph LR
    subgraph "External Ports"
        P3000[3000<br/>Frontend]
        P8002[8002<br/>Backend API]
        P8001[8001<br/>Main DB API]
        P8009[8009<br/>JD DB API]
        P8007[8007<br/>Archive DB API]
    end
    
    subgraph "Internal Container Ports"
        I3000[3000<br/>React Dev Server]
        I8002[8002<br/>Flask/Gunicorn]
        I8003[8003<br/>Main DB Service]
        I8004[8004<br/>JD DB Service]
        I8005[8005<br/>Archive DB Service]
    end
    
    P3000 --> I3000
    P8002 --> I8002
    P8001 --> I8003
    P8009 --> I8004
    P8007 --> I8005
    
    style P3000 fill:#e1f5fe
    style P8002 fill:#f3e5f5
    style P8001 fill:#e8f5e8
    style P8009 fill:#e8f5e8
    style P8007 fill:#e8f5e8
```

## Environment Configuration

### Backend Environment Variables (.env)
```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
PROXYCURL_API_KEY=your_proxycurl_api_key

# Database URLs
DATABASE_API_URL=http://database:8001
JD_DATABASE_API=http://jddatabase:8009
AR_DATABASE_API=http://ardatabase:8007

# Flask Configuration
SECRET_KEY=your_secret_key
MAX_CONTENT_LENGTH=5368709120

# Environment
ENVIRONMENT=development
DEBUG=true
```

### Frontend Environment Variables (.env)
```bash
# Backend API
REACT_APP_BACKEND_API=http://localhost:8002

# Azure AD Configuration
REACT_APP_CLIENT_ID=your_azure_client_id
REACT_APP_TENANT_ID=your_azure_tenant_id
REACT_APP_REDIRECT_URI=http://localhost:3000
```

## Deployment Strategies

### Development Deployment
```bash
# Setup script for development
#!/bin/bash

# Create Docker network
docker network create interviewGPT_network

# Start database services
cd db && docker compose up -d

# Build and start application services
cd ../config/docker && docker compose up --build
```

### Production Deployment
```mermaid
flowchart TD
    BUILD[Build Images] --> REGISTRY[Push to Registry]
    REGISTRY --> DEPLOY[Deploy to Production]
    
    subgraph "Build Process"
        BUILD_FE[Build Frontend]
        BUILD_BE[Build Backend]
        BUILD_DB[Build Database Images]
    end
    
    subgraph "Production Environment"
        LOAD_BALANCER[Load Balancer]
        APP_INSTANCES[Application Instances]
        DB_CLUSTER[Database Cluster]
        MONITORING[Monitoring & Logging]
    end
    
    BUILD --> BUILD_FE
    BUILD --> BUILD_BE
    BUILD --> BUILD_DB
    
    DEPLOY --> LOAD_BALANCER
    LOAD_BALANCER --> APP_INSTANCES
    APP_INSTANCES --> DB_CLUSTER
    APP_INSTANCES --> MONITORING
    
    style BUILD fill:#e8f5e8
    style DEPLOY fill:#e3f2fd
    style MONITORING fill:#fff3e0
```

## Scaling Considerations

### Horizontal Scaling
- **Frontend**: Multiple React app instances behind load balancer
- **Backend**: Multiple Flask API instances with shared database
- **Database**: Read replicas for improved performance

### Vertical Scaling
- **Memory**: Increase container memory limits for AI processing
- **CPU**: Multi-core processing for concurrent requests
- **Storage**: SSD storage for database performance

## Security Configuration

### Network Security
```mermaid
graph TB
    subgraph "External Network"
        INTERNET[Internet]
        FIREWALL[Firewall/WAF]
    end
    
    subgraph "DMZ"
        LOAD_BALANCER[Load Balancer]
        REVERSE_PROXY[Reverse Proxy]
    end
    
    subgraph "Application Network"
        FRONTEND[Frontend Containers]
        BACKEND[Backend Containers]
    end
    
    subgraph "Database Network"
        DATABASES[Database Containers]
    end
    
    INTERNET --> FIREWALL
    FIREWALL --> LOAD_BALANCER
    LOAD_BALANCER --> REVERSE_PROXY
    REVERSE_PROXY --> FRONTEND
    FRONTEND --> BACKEND
    BACKEND --> DATABASES
    
    style FIREWALL fill:#ffebee
    style DATABASES fill:#e8f5e8
```

### Container Security
- **Non-root users**: Containers run with non-privileged users
- **Read-only filesystems**: Immutable container filesystems
- **Secret management**: Environment variables for sensitive data
- **Network isolation**: Containers communicate only through defined networks

## Monitoring and Logging

### Application Monitoring
- **Health checks**: Container health monitoring
- **Performance metrics**: Response times and throughput
- **Error tracking**: Application error logging
- **Resource usage**: CPU, memory, and disk monitoring

### Log Management
```bash
# Log directory structure
logs/
├── application.log      # Main application logs
├── error.log           # Error logs
├── access.log          # API access logs
└── database.log        # Database operation logs
```

## Backup and Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup SQLite databases
cp db/server/candidateDB $BACKUP_DIR/
cp db/desc/jdDB $BACKUP_DIR/
cp db/archive/archiveDB $BACKUP_DIR/

# Backup uploaded files
tar -czf $BACKUP_DIR/uploads.tar.gz uploads/
```

### Disaster Recovery
- **Database replication**: Real-time database synchronization
- **File storage backup**: Regular backup of uploaded files
- **Configuration backup**: Version-controlled configuration files
- **Recovery procedures**: Documented recovery processes
