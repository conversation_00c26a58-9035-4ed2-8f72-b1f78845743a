# Functional Flows Documentation

This document details the key functional flows and business processes in InterviewBot-V2.

## Core Functional Flows

### 1. Interview Generation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant LLM as LLM Service
    participant DB as Database
    participant EXT as External APIs
    
    U->>F: Upload Resume & Job Description
    F->>F: Validate file formats
    F->>B: POST /generateInterview
    B->>B: Extract text from files
    
    alt LinkedIn Profile Provided
        B->>EXT: Fetch LinkedIn data (ProxyCurl)
        EXT-->>B: Profile data
    end
    
    B->>B: Generate prompts
    B->>LLM: Call OpenAI/Gemini API
    LLM-->>B: AI-generated questions
    B->>B: Parse and format response
    B-->>F: Interview questions
    F->>F: Display questions
    
    U->>F: Review and edit questions
    U->>F: Save interview
    F->>B: POST /saveCandidate
    B->>DB: Store interview data
    DB-->>B: Confirmation
    B-->>F: Success response
    F->>F: Show success message
```

### 2. User Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant AAD as Azure AD
    participant B as Backend
    
    U->>F: Access application
    F->>F: Check authentication status
    
    alt Not Authenticated
        F->>AAD: Redirect to login
        U->>AAD: Enter credentials
        AAD->>AAD: Validate credentials
        AAD-->>F: Return access token
        F->>F: Store token and user info
    end
    
    F->>F: Set active account
    F->>B: API calls with auth headers
    B->>B: Validate token (if needed)
    B-->>F: Protected resources
```

### 3. Job Description Management Flow

```mermaid
sequenceDiagram
    participant A as Admin User
    participant F as Frontend
    participant B as Backend
    participant JD_DB as JD Database
    participant AR_DB as Archive Database
    
    A->>F: Access Job Descriptions page
    F->>B: GET /getAllJD
    B->>JD_DB: Fetch all job descriptions
    JD_DB-->>B: JD list
    B-->>F: JD data
    F->>F: Display JD list
    
    alt Create New JD
        A->>F: Create new job description
        F->>F: Show JD form
        A->>F: Fill form and save
        F->>B: POST /saveJD
        B->>JD_DB: Store JD data
        JD_DB-->>B: Confirmation
        B-->>F: Success response
    end
    
    alt Archive JD
        A->>F: Archive job description
        F->>B: POST /saveAR
        B->>AR_DB: Move to archive
        AR_DB-->>B: Confirmation
        B->>JD_DB: Remove from active
        B-->>F: Success response
    end
```

### 4. Question Regeneration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant LLM as LLM Service
    
    U->>F: Request question regeneration
    F->>F: Show regeneration options
    U->>F: Select question type & count
    F->>B: POST /regenerateTechnical or /regenerateBehavioral
    B->>B: Prepare regeneration prompt
    B->>LLM: Call AI service
    LLM-->>B: New questions
    B->>B: Clean and format response
    B-->>F: Updated questions
    F->>F: Replace old questions
    F->>F: Show updated interview
```

## Business Process Flows

### 5. Complete Interview Lifecycle

```mermaid
flowchart TD
    START([Interview Request]) --> UPLOAD[Upload Documents]
    UPLOAD --> VALIDATE{Valid Files?}
    VALIDATE -->|No| ERROR[Show Error]
    VALIDATE -->|Yes| PROCESS[Process Documents]
    
    PROCESS --> LINKEDIN{LinkedIn Profile?}
    LINKEDIN -->|Yes| FETCH[Fetch LinkedIn Data]
    LINKEDIN -->|No| GENERATE
    FETCH --> GENERATE[Generate Questions]
    
    GENERATE --> AI_CALL[Call AI Service]
    AI_CALL --> PARSE[Parse Response]
    PARSE --> DISPLAY[Display Questions]
    
    DISPLAY --> REVIEW{User Review}
    REVIEW -->|Edit| MODIFY[Modify Questions]
    REVIEW -->|Regenerate| REGEN[Regenerate Specific Questions]
    REVIEW -->|Approve| SAVE[Save Interview]
    
    MODIFY --> DISPLAY
    REGEN --> AI_CALL
    
    SAVE --> STORE[Store in Database]
    STORE --> DOWNLOAD{Download Report?}
    DOWNLOAD -->|Yes| GENERATE_PDF[Generate PDF Report]
    DOWNLOAD -->|No| COMPLETE
    GENERATE_PDF --> COMPLETE[Interview Complete]
    
    COMPLETE --> ARCHIVE{Archive?}
    ARCHIVE -->|Yes| MOVE_ARCHIVE[Move to Archive]
    ARCHIVE -->|No| END([End])
    MOVE_ARCHIVE --> END
    
    ERROR --> END
    
    style START fill:#e8f5e8
    style COMPLETE fill:#e3f2fd
    style END fill:#fff3e0
```

### 6. User Role Management Flow

```mermaid
flowchart TD
    LOGIN[User Login] --> CHECK_ROLE{Check User Role}
    
    CHECK_ROLE -->|Admin| ADMIN_ACCESS[Full Access]
    CHECK_ROLE -->|User| USER_ACCESS[Limited Access]
    CHECK_ROLE -->|Power Admin| POWER_ADMIN[Super Admin Access]
    
    ADMIN_ACCESS --> ADMIN_FEATURES[
        • Create/Edit Job Descriptions
        • View All Interviews
        • Manage Users
        • Archive Management
    ]
    
    USER_ACCESS --> USER_FEATURES[
        • Create Interviews
        • View Own Interviews
        • Basic Dashboard
    ]
    
    POWER_ADMIN --> POWER_FEATURES[
        • All Admin Features
        • System Configuration
        • Advanced Settings
        • User Role Management
    ]
    
    ADMIN_FEATURES --> DASHBOARD[Dashboard Access]
    USER_FEATURES --> DASHBOARD
    POWER_FEATURES --> DASHBOARD
    
    style ADMIN_ACCESS fill:#ffebee
    style USER_ACCESS fill:#e8f5e8
    style POWER_ADMIN fill:#fff3e0
```

### 7. File Processing Flow

```mermaid
flowchart TD
    UPLOAD[File Upload] --> CHECK_TYPE{Check File Type}
    
    CHECK_TYPE -->|PDF| PDF_EXTRACT[Extract PDF Text]
    CHECK_TYPE -->|DOC/DOCX| DOC_EXTRACT[Extract Word Text]
    CHECK_TYPE -->|TXT| TXT_READ[Read Text File]
    CHECK_TYPE -->|Unsupported| ERROR[File Type Error]
    
    PDF_EXTRACT --> VALIDATE_TEXT{Text Extracted?}
    DOC_EXTRACT --> VALIDATE_TEXT
    TXT_READ --> VALIDATE_TEXT
    
    VALIDATE_TEXT -->|Yes| CLEAN_TEXT[Clean and Format Text]
    VALIDATE_TEXT -->|No| EXTRACT_ERROR[Extraction Error]
    
    CLEAN_TEXT --> ANALYZE[Analyze Content]
    ANALYZE --> GENERATE_PROMPTS[Generate AI Prompts]
    GENERATE_PROMPTS --> SUCCESS[Processing Complete]
    
    ERROR --> END[End with Error]
    EXTRACT_ERROR --> END
    SUCCESS --> CONTINUE[Continue to AI Processing]
    
    style UPLOAD fill:#e8f5e8
    style SUCCESS fill:#e3f2fd
    style ERROR fill:#ffebee
    style EXTRACT_ERROR fill:#ffebee
```

## Error Handling Flows

### 8. API Error Handling

```mermaid
flowchart TD
    API_CALL[API Request] --> NETWORK{Network Available?}
    
    NETWORK -->|No| NETWORK_ERROR[Network Error]
    NETWORK -->|Yes| SEND_REQUEST[Send Request]
    
    SEND_REQUEST --> RESPONSE{Response Status}
    
    RESPONSE -->|200-299| SUCCESS[Success Response]
    RESPONSE -->|400-499| CLIENT_ERROR[Client Error]
    RESPONSE -->|500-599| SERVER_ERROR[Server Error]
    RESPONSE -->|Timeout| TIMEOUT_ERROR[Timeout Error]
    
    SUCCESS --> PROCESS_DATA[Process Response Data]
    
    CLIENT_ERROR --> LOG_ERROR[Log Error]
    SERVER_ERROR --> LOG_ERROR
    TIMEOUT_ERROR --> LOG_ERROR
    NETWORK_ERROR --> LOG_ERROR
    
    LOG_ERROR --> RETRY{Retry Available?}
    RETRY -->|Yes| WAIT[Wait and Retry]
    RETRY -->|No| SHOW_ERROR[Show User Error]
    
    WAIT --> SEND_REQUEST
    SHOW_ERROR --> END[End with Error]
    PROCESS_DATA --> END_SUCCESS[End Successfully]
    
    style SUCCESS fill:#e8f5e8
    style END_SUCCESS fill:#e3f2fd
    style SHOW_ERROR fill:#ffebee
```

## Integration Points

### 9. External Service Integration

```mermaid
flowchart LR
    subgraph "InterviewBot-V2"
        BACKEND[Backend Service]
        LLM_SVC[LLM Service]
    end
    
    subgraph "External APIs"
        OPENAI[OpenAI GPT-4]
        GEMINI[Google Gemini]
        PROXYCURL[ProxyCurl LinkedIn]
    end
    
    subgraph "Authentication"
        AZURE_AD[Azure Active Directory]
    end
    
    BACKEND --> LLM_SVC
    LLM_SVC --> OPENAI
    LLM_SVC --> GEMINI
    BACKEND --> PROXYCURL
    
    BACKEND -.-> AZURE_AD
    
    style BACKEND fill:#f3e5f5
    style LLM_SVC fill:#fff3e0
    style OPENAI fill:#ffebee
    style GEMINI fill:#e8f5e8
    style PROXYCURL fill:#e3f2fd
    style AZURE_AD fill:#fff8e1
```

## Performance Considerations

- **Async Processing**: Long-running AI calls are handled asynchronously
- **Caching**: Frequently accessed data is cached
- **Database Optimization**: Separate databases for different data types
- **File Processing**: Efficient text extraction and processing
- **Error Recovery**: Robust error handling and retry mechanisms
