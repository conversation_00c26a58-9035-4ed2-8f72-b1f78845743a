from flask import Flask, request, jsonify
import json
import sqlite3

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 5 * 1024 * 1024 * 1024  # 5 GB

def connect_to_db():
    """
    Connects to the candidate database.

    Returns:
        conn: Connection object to the candidate database.
    """
    conn = sqlite3.connect('./candidateDB')
    return conn


def add_new_column():
    """
    Adds a new column named 'user' to the 'candidates' table if it doesn't exist.
    """
    try:
        conn = connect_to_db()
        cursor = conn.cursor()

        # Check if the 'user' column exists
        cursor.execute("PRAGMA table_info(candidates)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'user' not in column_names:
            # Add the 'user' column
            cursor.execute("ALTER TABLE candidates ADD COLUMN user TEXT DEFAULT ''")

        conn.commit()
    except sqlite3.Error as e:
        print(f"Error adding the 'user' column: {e}")
    finally:
        conn.close()


def create_db_table():
    """
    Creates a database table called 'candidates' if it doesn't already exist.
    The table has the following columns:
    - id: INTEGER (Primary Key)
    - uuid: TEXT
    - ceipal TEXT
    - date: TEXT
    - nickName: TEXT
    - name: TEXT
    - job: TEXT
    - data: TEXT
    - status: TEXT
    - applicantNO TEXT,
    - jobCode TEXT,
    - clientName TEXT,
    - clientManager TEXT,
    """
    try:
        conn = connect_to_db()
        conn.execute('''CREATE TABLE IF NOT EXISTS candidates (
                    id INTEGER PRIMARY KEY,
                    uuid TEXT,
                    ceipal TEXT,
                    date TEXT,
                    nickName TEXT,
                    name TEXT,
                    job TEXT,
                    status TEXT,
                    applicantNO TEXT,
                    jobCode TEXT,
                    clientName TEXT,
                    clientManager TEXT,
                    data TEXT
                )''')

        conn.commit()
    except sqlite3.Error as e:
        print(f"Error creating the 'candidates' table: {e}")
    finally:
        conn.close()
        add_new_column()

# This route gets all interviews general data
@app.route('/getAll', methods=['GET'])
def getAllCandidates():
    """
    Retrieves all candidates from the database and returns them as a JSON response.

    Returns:
        A tuple containing the JSON response with all candidates and the HTTP status code 200.
    """
    create_db_table()
    candidates = []
    try:
        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT * FROM candidates")
        rows = cur.fetchall()

        # convert row objects to dictionary
        for i in rows:
            candidate = {}
            candidate["Candidate_Name"] = i["name"]
            candidate["Job_Description_Title"] = i["job"]
            candidate["date"] = i["date"]
            candidate["Nick_Name"] = i["nickName"]
            candidate["user"] = i["user"]
            candidate["uuid"] = i["uuid"]
            candidate["ceipal"] = i["ceipal"]
            candidate["Status"]= i["status"]
            candidate["Applicant_No"] = i["applicantNO"]
            candidate["Job_Code"] = i["jobCode"]
            candidate["Client_Name"] = i["clientName"]
            candidate["Client_Manager"] = i["clientManager"]

            candidates.append(candidate)

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        candidates = []

    return jsonify(candidates), 200

# This route gets a single interview
@app.route('/getCandidate', methods=['POST'])
def get_data():
    """
    Retrieves candidate data from the database based on the provided UUID.

    Returns:
        A JSON response containing the candidate data.
    """
    try:
        json_data = request.get_json()
        uuid = json_data.get('uuid')

        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT data FROM candidates WHERE uuid = ?", (uuid,))
        row = cur.fetchone()

        candidateData = row["data"]
        json_data = json.loads(candidateData)
    except:
        json_data = []

    return jsonify({"candidateData": json_data}), 200

# This route handles adding and editing data in candidate db
@app.route('/saveCandidate', methods=['POST'])
def saveCandidateData():
    create_db_table()
    try:
        json_data = request.get_json()
        candidateUuid = json_data.get('uuid')
        candidateName = json_data.get('Candidate_Name')
        candidateJob = json_data.get('Job_Description_Title')
        interviewDate = json_data.get('date')
        nickName = json_data.get('Nick_Name')
        user = json_data.get('user')
        ceipal = json_data.get('ceipal')
        status = json_data.get("Status")
        jobCode = json_data.get("Job_Code")
        clientName = json_data.get("Client_Name")
        clientManager = json_data.get("Client_Manager")
        applicantNO = json_data.get("Applicant_NO")
        candidateDataStr = json.dumps(json_data)

        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT id FROM candidates WHERE uuid = ?", (candidateUuid,))
        row = cur.fetchone()

        if(row is not None):
            cur.execute("""
            UPDATE candidates 
            SET data = ?, 
                name = ?, 
                job = ?, 
                date = ?, 
                nickName = ?, 
                status = ?, 
                user = ?, 
                jobCode = ?, 
                clientName = ?, 
                clientManager = ?, 
                ceipal = ?, 
                applicantNO = ? 
            WHERE uuid = ? 
        """, (
            candidateDataStr,
            candidateName,
            candidateJob,
            interviewDate,
            nickName,
            status,
            user,
            jobCode,
            clientName,
            clientManager,
            ceipal,
            applicantNO,
            candidateUuid
        ))
        else:
            cur.execute("INSERT INTO candidates (uuid, ceipal ,date, nickName, name, job, status, user, jobCode, clientName, clientManager, applicantNO, data ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (candidateUuid, ceipal, interviewDate, nickName, candidateName, candidateJob, status, user, jobCode, clientName, clientManager, applicantNO, candidateDataStr))

        conn.commit()
    except:
        conn().rollback()
    finally:
        conn.close()

    return jsonify({"message": "Successfully Saved To DataBase"}), 200

# Route to delete an interview from database
@app.route('/deleteInterview', methods=['POST'])
def delete_interview():
    message = {}
    try:
        json_data = request.get_json()
        uuid = json_data.get('uuid')
        conn = connect_to_db()
        conn.execute("DELETE from candidates WHERE uuid = ?",     
                      (uuid,))
        conn.commit()
        message["status"] = "User deleted successfully"
    except:
        conn.rollback()
        message["status"] = "Cannot delete user"
    finally:
        conn.close()

    return jsonify(message), 200

if __name__ == "__main__":
    create_db_table()
    app.run(host='0.0.0.0', port=8001, debug=False)