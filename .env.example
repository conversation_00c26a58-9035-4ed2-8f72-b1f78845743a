# InterviewBot-V2 Environment Variables Example
# Copy this file to create your environment configuration

# =============================================================================
# BACKEND ENVIRONMENT VARIABLES (app/backend/.env)
# =============================================================================

# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-dummy1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef

# Google Gemini API Configuration (Alternative LLM)
# Get your API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=AIzaSyDummy-1234567890abcdefghijklmnopqrstuvwxyz

# ProxyCurl API Configuration (LinkedIn Profile Scraping)
# Get your API key from: https://nubela.co/proxycurl/
PROXYCURL_API_KEY=dummy_proxycurl_api_key_1234567890abcdefghijklmnop

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES (app/frontend/.env)
# =============================================================================

# Backend API Configuration
# This should match the custom header key from nginx.conf
REACT_APP_BACKEND_API=DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC

# Backend API Base URL
REACT_APP_API_BASE_URL=http://localhost:8002

# Application Environment
REACT_APP_ENV=development

# Azure AD Configuration
REACT_APP_AZURE_CLIENT_ID=04efb143-adb9-43eb-ab40-e8bfca657adb
REACT_APP_AZURE_AUTHORITY=https://login.microsoftonline.com/creospan.com

# Development URLs
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_POST_LOGOUT_REDIRECT_URI=http://localhost:3000

# Production URLs (uncomment for production)
# REACT_APP_REDIRECT_URI=https://interviews.creospan.com
# REACT_APP_POST_LOGOUT_REDIRECT_URI=https://interviews.creospan.com

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Network
DOCKER_NETWORK=interviewGPT_network

# Service Ports
FRONTEND_PORT=3000
BACKEND_PORT=8002
DATABASE_PORT=8003
JD_DATABASE_PORT=8004
ARCHIVE_DATABASE_PORT=8005

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Custom Header for API Authentication
CUSTOM_HEADER_KEY=my_custom_header_key
CUSTOM_HEADER_VALUE=DLzw13EqCmBn60PTkgXZ76JCZY66y7Y2ULN46b1oLypNq7Sbb367dKEy6gs22Gb7J8rE05T45Kh5f0YrU0cZ326ms5LARkm3UhV01Ry65BiB8RhDtmHgSJpw8TgsdZVC

# CORS Origins
CORS_ORIGINS=http://localhost:3000,https://interviews.creospan.com

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# Maximum file size (5GB in bytes)
MAX_FILE_SIZE=5368709120

# Allowed file extensions
ALLOWED_EXTENSIONS=.pdf,.docx,.doc,.txt

# Upload directory
UPLOAD_FOLDER=./uploads

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=DEBUG

# Log file path
LOG_FILE=./logs/app.log

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Gunicorn workers
WORKERS=4

# Request timeout (seconds)
TIMEOUT=1600

# Maximum content length (5GB)
MAX_CONTENT_LENGTH=5368709120
