import docx
from docx.opc.constants import RELATIONSHIP_TYPE as RT
from PyPDF2 import PdfReader
import re
import subprocess
import json
# from spire.pdf import *
# from spire.pdf.common import *
 



# file contains all llm prompts and the functionality to parse and read uploaded documents

# COMMENTED OUT: LinkedIn functionality disabled for new interviews
# def get_linkedin_prompt(resume_text, linkedin_data):
#     prompt = f'''
#     Here is a candidates resume:
#
#     {resume_text}
#
#     And here is the candidates linkedin profile data:
#
#     {linkedin_data}
#
#     Compare their linkedin profile with their resume. Note any key differences, especially skills and experience listed on their resume that is not on their linkedin profile. Also note if they do not have a profile picture on their linkedin account since this is typically a sign of a bad candidate.
#     '''
#     return prompt


def get_main_prompts(resume_text, job_text, linkedInID):
    prompts = [
        # General Questions index 0
        f'''
        Here is a job description for an open position:

        {job_text}
        
        Here is a candidate's resume that is interviewing for this position:
        
        {resume_text}
        
        Generate 3 behavioral interview questions for the interviewer to ask the candidate considering the candidate's experience as expressed through the resume that will help the interviewer determine whether the candidate is a good fit for the job. Can you also highlight the important things to look for in their answer. Keep in mind that each question should only ask for one thing at a time, to avoid a situation where the candidate selectively only answers one of the things you ask for. Also, the question should be specific to the candidate's resume. Format your response as an array of JSON objects with two keys per object with values question and answer.
        ''',

        # Technical Questions index 1
        f'''
        Here is a job description for an open position:

        {job_text}
        
        Here is a candidate's resume that is interviewing for this position:
        
        {resume_text}

        Generate 5 close-ended, technical questions based on what technical skills are in the job description or title that the candidate also claims to know based on their resume that determines whether they are qualified for the job. The questions should not be based on their experience (such as "Recall a time when...") but rather close-ended questions that have a definitive answer (such as "How would you...?"). Also provide what those answers are and what the interviewer should look for in the candidates response.
        The questions should be difficult. Format your response as a array of JSON objects with two keys per object with values question and answer.
        ''',

        # Lacking Qualities index 2
        f'''
        Here is a job description for an open position:

        {job_text}
        
        Here is a candidate's resume that is interviewing for this position:
        
        {resume_text}

        Identify what skills, if any, are in the job description that are absent from the resume. Format your response as a simple array of string values describing what qualities seem to be lacking. If they are not lacking any skills still provide an array of a single string value stating that they are qualified in all areas of interest. Do not add any sentences before, just only return the data as instructed.
        ''',

        # Candidate Score index 3
        f'''I am interviewing a candidate for the following job description:
        
        {job_text}
        
        Here is the candidate's resume:
        
        {resume_text}
        
        Score this candidate from 1-10 for the categories of education level, work experience, job fit, communication skills, and technical skills. If they are underqualified for aspects of the job, they should score lower than a 5. Give a two to three sentence justification for each score.
        ''',

        # Candidate Assessment index 4
        f'''Here is a candidate's resume:
        
        {resume_text}
        
        Determine the candidate's qualifications based on their resume by deciding what percent of each position they are. For example, someone's resume might indicate that they are 40% frontend developent, 30% machine learning, and 30% cloud engineering. Also provide a two to three sentence justification for each percentage.
        ''',
        
        # Extracting Key Data index 5
        f'''I am interviewing a candidate for the following job description:
        
        {job_text}
        
        Here is the candidate's resume:
        
        {resume_text}
        
        Geneate a JSON object with key values 'Job_Title', 'Job_Description_Title', 'Candidate_Name' and 'Linkedin_ID' with the approprate values extracted from the information provided above. Linkedin ID's are found in any linkedin url provided in the candidates resume. User's can also provide their linkedInID, this is the user's LinkedInID if provided: {linkedInID} , If the user provides an address, example: https://linkedin.com/in/ravi the Linkedin_ID is ravi' . For example, if they provide the address https://linkedin.com/in/johnrmarty then the Linkedin_ID is 'johnrmarty'. Some resumes might provide a shortened address, for example, linkedin.com/in/daniel-managlia-3aa195149/ in this case the Linkedin_ID is daniel-managlia-3aa195149. (It will always be the last string in the url). If you do not detect the linkedin ID set the value to 'NOT FOUND' in the JSON object. If you cannot find the candidates current job title set 'Job_Title' to 'NONE'. Only return the json object, nothing else, no text and no explination JUST THE OBJECT.
        ''',

        # Candidate Strengths index 6
        f'''I am interviewing a candidate for the following job description:
        
        {job_text}
        
        Here is the candidate's resume:
        
        {resume_text}
        
        Generate a bullet pointed list describing the strengths of the candidate as they pertain to the job description. The qualities/strengths you identify should be technical and specific to the job, not general strengths such as communication. Be as descriptive as possible to create a convincing argument for hiring the candidate for the role. The list should have two newline characters (backslash n backslash n) seperating each point.
        ''',
    ]
    return prompts

def regenerate_technical(job_text, report, number):
    prompt = f'''   Here is a job description for an open position:

        {job_text}
        
        Here is a candidate's report info that is interviewing for this position:
        
        {report}

        Re-Generate a close-ended, technical question for question number {number} based on what technical skills are in the job description or title that the candidate also claims to know based on their report that determines whether they are qualified for the job. The question should not be based on their experience (such as "Recall a time when...") but rather close-ended question that have a definitive answer (such as "How would you...?"). Also provide what those answers are and what the interviewer should look for in the candidates response.
        The question should be difficult. Format your response as a array of JSON objects with two keys per object with values question and answer. '''

    return prompt

def regenerate_behavioral(job_text, report, number):
    prompt = f''' Here is a job description for an open position:

        {job_text}
        
        Here is a candidate's resume that is interviewing for this position:
        
        {report}
        
        Re-Generate a behavioral interview question for behavioral question number {number} for the interviewer to ask the candidate considering the candidate's experience as expressed through the report that will help the interviewer determine whether the candidate is a good fit for the job. Can you also highlight the important things to look for in their answer. Keep in mind that each question should only ask for one thing at a time, to avoid a situation where the candidate selectively only answers one of the things you ask for. Also, the question should be specific to the candidate's resume. Format your response as an array of JSON objects with two keys per object with values question and answer.'''

    return prompt

supported_file_types = {
    "application/pdf": ".pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
    "text/plain": ".txt",
    "application/msword": ".doc"
}

supported_file_types_list = [val for val in supported_file_types.values()]

def check_file_extension(uploaded_file):
    """
    Check the file extension of the uploaded file and return its content.

    Args:
        uploaded_file: The uploaded file object.

    Returns:
        The content of the uploaded file.

    Raises:
        Exception: If file processing fails.
    """
    try:
        file_content = ""
        if isinstance(uploaded_file, str):
            file_content = uploaded_file
        else:
            if not hasattr(uploaded_file, 'content_type'):
                raise Exception("Invalid file object - missing content_type")

            if uploaded_file.content_type == "application/pdf":
                file_content = parse_pdf(uploaded_file)
            elif uploaded_file.content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                file_content = parse_docx(uploaded_file)
            elif uploaded_file.content_type == "text/plain":
                file_content = uploaded_file.read().decode("utf-8")
            elif uploaded_file.content_type == "application/msword":
                file_content = parse_doc(uploaded_file)
            else:
                raise Exception(f"Unsupported file type: {uploaded_file.content_type}")

        if not file_content or len(file_content.strip()) == 0:
            raise Exception("File appears to be empty or contains no readable text")

        return file_content
    except Exception as e:
        print(f"Error in check_file_extension: {str(e)}")
        raise

def parse_pdf(file):
    try:
        pdf = PdfReader(file)
        output = []
        for page in pdf.pages:
          #  extract_images(page=page)
            text = page.extract_text()
            # Merge hyphenated words
            text = re.sub(r"(\w+)-\n(\w+)", r"\1\2", text)
            # Fix newlines in the middle of sentences
            text = re.sub(r"(?<!\n\s)\n(?!\s\n)", " ", text.strip())
            # Remove multiple newlines
            text = re.sub(r"\n\s*\n", "\n\n", text)

            output.append(text)

        # Join all pages into a single string
        return "\n\n".join(output)
    except Exception as e:
        print(f"Error parsing PDF: {str(e)}")
        raise Exception(f"Failed to parse PDF file: {str(e)}")

def parse_docx(file):
    try:
        doc = docx.Document(file)
        rels = doc.part.rels

        # Create a list to store the extracted hyperlinks
        extracted_hyperlinks = []
        for hyperlink in iter_hyperlink_rels(rels):
            extracted_hyperlinks.append(hyperlink)

        # Join the hyperlinks into a single string
        hyperlinks_string = '\n'.join(extracted_hyperlinks)

        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + '\n'

        return text + hyperlinks_string
    except Exception as e:
        print(f"Error parsing DOCX: {str(e)}")
        raise Exception(f"Failed to parse DOCX file: {str(e)}")

def iter_hyperlink_rels(rels):
    """
    Iterate over the hyperlink relationships in the given dictionary of relationships.

    Args:
        rels (dict): A dictionary of relationships.

    Yields:
        str: The target of each hyperlink relationship.

    """
    for rel in rels:
        if rels[rel].reltype == RT.HYPERLINK:
            yield rels[rel]._target

import subprocess

def parse_doc(doc_file):
    """
    Parses a DOC file using the 'antiword' command-line tool.

    Args:
        doc_file (file-like object): The DOC file to be parsed.

    Returns:
        str: The parsed content of the DOC file as a string, or None if an error occurs.
    """
    try:
        doc_file_data = doc_file.read()

        process = subprocess.Popen(
            ['antiword', '-'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        stdout, stderr = process.communicate(input=doc_file_data)
        if stderr:
            print(f"Error: {stderr}")
            
        return stdout

    except subprocess.CalledProcessError:
        return None
    

# def extract_PDFimages(file):
#     pdf = PdfDocument()
#
#     pdf.LoadFromFile(file)
#
#     images = []
#
#     for i in range(pdf.Pages.Count):
#         page = pdf.Pages.get_Item(i)
#         for img in page.ExtractImages():
#             images.append(img)
#
#     i = 0
#     for image in images:
#         i += 1
#         image.Save("/tmp/Image-{0:d}.png".format(i), ImageFormat.get_Png())
#
#     pdf.Close()
#     return images

def extract_PDFimages(file):
    """Placeholder function - Spire.Pdf not available"""
    print("Warning: PDF image extraction not available (Spire.Pdf not installed)")
    return []

def extract_text_from_file(uploaded_file):
    """
    Extract text from uploaded file.
    This is an alias for check_file_extension for compatibility.

    Args:
        uploaded_file: The uploaded file object.

    Returns:
        str: The extracted text content.
    """
    return check_file_extension(uploaded_file)

def is_valid_json(json_string):
    """
    Check if a string is valid JSON.

    Args:
        json_string (str): The string to validate.

    Returns:
        bool: True if valid JSON, False otherwise.
    """
    try:
        json.loads(json_string)
        return True
    except (json.JSONDecodeError, TypeError):
        return False

