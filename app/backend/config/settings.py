"""
Configuration settings for the InterviewBot backend application.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Base configuration class."""
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024 * 1024  # 5 GB
    
    # OpenAI API Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    
    # Google Gemini API Configuration (Optional)
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')
    
    # ProxyCurl API Configuration (Optional)
    PROXYCURL_API_KEY = os.getenv('PROXYCURL_API_KEY', '')
    
    # Database API URLs
    DATABASE_API_URL = os.getenv('DATABASE_API_URL', 'http://localhost:8001')
    JD_DATABASE_API = os.getenv('JD_DATABASE_API', 'http://localhost:8009')
    AR_DATABASE_API = os.getenv('AR_DATABASE_API', 'http://localhost:8007')
    
    # File Upload Configuration
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'doc', 'docx'}

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    FLASK_ENV = 'production'

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Get configuration based on environment."""
    env = os.getenv('FLASK_ENV', 'development')
    return config.get(env, config['default'])
