"""
LLM service module for handling AI/ML inference operations.
"""
import json
from llm_inference import openai_api_GPT4_st, google_gemeni_st
from utils import get_main_prompts, regenerate_technical, regenerate_behavioral

class LLMService:
    """Service class for LLM operations."""
    
    def __init__(self):
        self.default_llm = "openai"  # Default to OpenAI
    
    def call_llm(self, prompt, llm_type="openai"):
        """
        Call the specified LLM with the given prompt.
        
        Args:
            prompt (str): The prompt to send to the LLM
            llm_type (str): Type of LLM to use ("openai" or "gemini")
            
        Returns:
            dict or str: The response from the LLM
        """
        if llm_type == "openai":
            return openai_api_GPT4_st(prompt)
        elif llm_type == "gemini":
            return google_gemeni_st(prompt)
        else:
            raise ValueError(f"Unsupported LLM type: {llm_type}")
    
    def clean_response(self, response):
        """
        Clean and parse the LLM response.
        
        Args:
            response: The raw response from the LLM
            
        Returns:
            list: Cleaned and parsed response data
        """
        final = []
        
        # Handle OpenAI API response format
        if isinstance(response, dict) and 'choices' in response:
            response = response['choices'][0]['message']['content']
        
        try:
            response = json.loads(response)
        except json.JSONDecodeError:
            pass
        
        if isinstance(response, list):
            for item in response:
                if isinstance(item, dict):
                    final.append(item)
                else:
                    try:
                        parsed_item = json.loads(item)
                        final.append(parsed_item)
                    except (json.JSONDecodeError, TypeError):
                        final.append({"content": str(item)})
        elif isinstance(response, dict):
            final.append(response)
        else:
            try:
                parsed_response = json.loads(str(response))
                final.append(parsed_response)
            except (json.JSONDecodeError, TypeError):
                final.append({"content": str(response)})
        
        return final
    
    def generate_interview_questions(self, resume_text, job_text, linkedin_id=None):
        """
        Generate interview questions based on resume and job description.
        
        Args:
            resume_text (str): The candidate's resume text
            job_text (str): The job description text
            linkedin_id (str, optional): LinkedIn profile ID
            
        Returns:
            dict: Generated interview questions and analysis
        """
        result = {
            "technical_questions": [],
            "behavioral_questions": [],
            "analysis": {}
        }
        
        try:
            prompts = get_main_prompts(resume_text, job_text, linkedin_id)
            
            for prompt in prompts:
                response = self.call_llm(prompt, self.default_llm)
                
                # Handle OpenAI API response format
                if isinstance(response, dict) and 'choices' in response:
                    response = response['choices'][0]['message']['content']
                
                try:
                    response = json.loads(response)
                except json.JSONDecodeError:
                    pass
                
                if isinstance(response, dict):
                    if "technical_questions" in response:
                        result["technical_questions"].extend(response["technical_questions"])
                    if "behavioral_questions" in response:
                        result["behavioral_questions"].extend(response["behavioral_questions"])
                    if "analysis" in response:
                        result["analysis"].update(response["analysis"])
                elif isinstance(response, list):
                    # Assume it's a list of questions
                    result["technical_questions"].extend(response)
            
            return result
            
        except Exception as e:
            raise Exception(f"Error generating interview questions: {str(e)}")
    
    def regenerate_technical_questions(self, job_text, report, number):
        """
        Regenerate technical questions.
        
        Args:
            job_text (str): The job description text
            report (str): Previous report/analysis
            number (int): Number of questions to generate
            
        Returns:
            list: Regenerated technical questions
        """
        try:
            prompt = regenerate_technical(job_text, report, number)
            response = self.call_llm(prompt, self.default_llm)
            return self.clean_response(response)
        except Exception as e:
            raise Exception(f"Error regenerating technical questions: {str(e)}")
    
    def regenerate_behavioral_questions(self, job_text, report, number):
        """
        Regenerate behavioral questions.
        
        Args:
            job_text (str): The job description text
            report (str): Previous report/analysis
            number (int): Number of questions to generate
            
        Returns:
            list: Regenerated behavioral questions
        """
        try:
            prompt = regenerate_behavioral(job_text, report, number)
            response = self.call_llm(prompt, self.default_llm)
            return self.clean_response(response)
        except Exception as e:
            raise Exception(f"Error regenerating behavioral questions: {str(e)}")
