"""
Database service module for handling database operations.
"""
import requests
from flask import abort
from config.settings import get_config

config = get_config()

class DatabaseService:
    """Service class for database operations."""
    
    def __init__(self):
        self.database_api_url = config.DATABASE_API_URL
        self.jd_database_api = config.JD_DATABASE_API
        self.ar_database_api = config.AR_DATABASE_API
    
    def forward_request(self, database_url, route, method, json_data=None):
        """
        Forward requests to the appropriate database service.
        
        Args:
            database_url (str): The database API URL
            route (str): The API route
            method (str): HTTP method (GET, POST, etc.)
            json_data (dict, optional): JSON data for POST requests
            
        Returns:
            tuple: (response_data, status_code)
        """
        try:
            if method == 'GET':
                response = requests.get(f"{database_url}/{route}")
            elif method == 'POST':
                response = requests.post(f"{database_url}/{route}", json=json_data)
            else:
                return abort(400, "Invalid HTTP method")

            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx and 5xx)
            return response.json(), response.status_code

        except requests.RequestException as e:
            return abort(500, f"Error forwarding request: {e}")
    
    def get_all_candidates(self):
        """Get all candidates from the database."""
        return self.forward_request(self.database_api_url, "getAll", "GET")
    
    def get_candidate(self, json_data):
        """Get a specific candidate from the database."""
        return self.forward_request(self.database_api_url, "getCandidate", "POST", json_data)
    
    def add_candidate(self, json_data):
        """Add a new candidate to the database."""
        return self.forward_request(self.database_api_url, "addCandidate", "POST", json_data)
    
    def update_candidate(self, json_data):
        """Update an existing candidate in the database."""
        return self.forward_request(self.database_api_url, "updateCandidate", "POST", json_data)
    
    def delete_candidate(self, json_data):
        """Delete a candidate from the database."""
        return self.forward_request(self.database_api_url, "deleteCandidate", "POST", json_data)
    
    # Job Description Database Operations
    def get_all_job_descriptions(self):
        """Get all job descriptions from the database."""
        return self.forward_request(self.jd_database_api, "getAll", "GET")
    
    def get_job_description(self, json_data):
        """Get a specific job description from the database."""
        return self.forward_request(self.jd_database_api, "getJD", "POST", json_data)
    
    def add_job_description(self, json_data):
        """Add a new job description to the database."""
        return self.forward_request(self.jd_database_api, "addJD", "POST", json_data)
    
    def update_job_description(self, json_data):
        """Update an existing job description in the database."""
        return self.forward_request(self.jd_database_api, "updateJD", "POST", json_data)
    
    def delete_job_description(self, json_data):
        """Delete a job description from the database."""
        return self.forward_request(self.jd_database_api, "deleteJD", "POST", json_data)
    
    # Archive Database Operations
    def get_all_archived_candidates(self):
        """Get all archived candidates from the database."""
        return self.forward_request(self.ar_database_api, "getAll", "GET")
    
    def get_archived_candidate(self, json_data):
        """Get a specific archived candidate from the database."""
        return self.forward_request(self.ar_database_api, "getCandidate", "POST", json_data)
    
    def add_archived_candidate(self, json_data):
        """Add a new archived candidate to the database."""
        return self.forward_request(self.ar_database_api, "addCandidate", "POST", json_data)
    
    def delete_archived_candidate(self, json_data):
        """Delete an archived candidate from the database."""
        return self.forward_request(self.ar_database_api, "deleteCandidate", "POST", json_data)
