"""
API routes for interview operations.
"""
import json
from flask import Blueprint, request, jsonify
from services.llm_service import LLMService
from services.database_service import DatabaseService
from utils import extract_text_from_file, is_valid_json

interview_bp = Blueprint('interview', __name__)
llm_service = LLMService()
db_service = DatabaseService()

@interview_bp.route('/generateQuestions', methods=['POST'])
def generate_questions():
    """Generate interview questions based on uploaded files."""
    try:
        # Get uploaded files
        resume_file = request.files.get('resume')
        job_file = request.files.get('jobDescription')
        linkedin_id = request.form.get('linkedInID', '')
        
        if not resume_file or not job_file:
            return jsonify({"error": "Both resume and job description files are required"}), 400
        
        # Extract text from files
        resume_text = extract_text_from_file(resume_file)
        job_text = extract_text_from_file(job_file)
        
        if not resume_text or not job_text:
            return jsonify({"error": "Could not extract text from uploaded files"}), 400
        
        # Generate interview questions
        result = llm_service.generate_interview_questions(resume_text, job_text, linkedin_id)
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"error": f"Error generating questions: {str(e)}"}), 500

@interview_bp.route('/regenerateTechnical', methods=['POST'])
def regenerate_technical():
    """Regenerate technical interview questions."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        job_text = json_data.get('jobText', '')
        report = json_data.get('report', '')
        number = json_data.get('number', 5)
        
        if not job_text:
            return jsonify({"error": "Job description text is required"}), 400
        
        result = llm_service.regenerate_technical_questions(job_text, report, number)
        return jsonify({"questions": result}), 200
        
    except Exception as e:
        return jsonify({"error": f"Error regenerating technical questions: {str(e)}"}), 500

@interview_bp.route('/regenerateBehavioral', methods=['POST'])
def regenerate_behavioral():
    """Regenerate behavioral interview questions."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        job_text = json_data.get('jobText', '')
        report = json_data.get('report', '')
        number = json_data.get('number', 5)
        
        if not job_text:
            return jsonify({"error": "Job description text is required"}), 400
        
        result = llm_service.regenerate_behavioral_questions(job_text, report, number)
        return jsonify({"questions": result}), 200
        
    except Exception as e:
        return jsonify({"error": f"Error regenerating behavioral questions: {str(e)}"}), 500
