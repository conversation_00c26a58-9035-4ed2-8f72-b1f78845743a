"""
API routes for candidate operations.
"""
from flask import Blueprint, request, jsonify
from services.database_service import DatabaseService

candidate_bp = Blueprint('candidate', __name__)
db_service = DatabaseService()

@candidate_bp.route('/getAll', methods=['GET'])
def get_all_candidates():
    """Get all candidates from the database."""
    try:
        data, status_code = db_service.get_all_candidates()
        return jsonify(data), status_code
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@candidate_bp.route('/getCandidate', methods=['POST'])
def get_candidate():
    """Get a specific candidate from the database."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        data, status_code = db_service.get_candidate(json_data)
        return jsonify(data), status_code
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@candidate_bp.route('/addCandidate', methods=['POST'])
def add_candidate():
    """Add a new candidate to the database."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        data, status_code = db_service.add_candidate(json_data)
        return jsonify(data), status_code
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@candidate_bp.route('/updateCandidate', methods=['POST'])
def update_candidate():
    """Update an existing candidate in the database."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        data, status_code = db_service.update_candidate(json_data)
        return jsonify(data), status_code
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@candidate_bp.route('/deleteCandidate', methods=['POST'])
def delete_candidate():
    """Delete a candidate from the database."""
    try:
        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        data, status_code = db_service.delete_candidate(json_data)
        return jsonify(data), status_code
    except Exception as e:
        return jsonify({"error": str(e)}), 500
