# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API Configuration (Alternative LLM) - OPTIONAL
# Get your API key from: https://aistudio.google.com/app/apikey
# Leave empty if only using OpenAI
GEMINI_API_KEY=

# ProxyCurl API Configuration (LinkedIn Profile Scraping) - OPTIONAL
# Get your API key from: https://nubela.co/proxycurl/
# Leave empty if not using LinkedIn features
PROXYCURL_API_KEY=

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///candidateDB
JD_DATABASE_URL=sqlite:///jdDB
ARCHIVE_DATABASE_URL=sqlite:///archiveDB

# Database API URLs (for microservices)
DATABASE_API_URL=http://database:8001
JD_DATABASE_API=http://jddatabase:8009
AR_DATABASE_API=http://ardatabase:8007

# Gunicorn Configuration
WORKERS=4
TIMEOUT=1600
BIND_ADDRESS=0.0.0.0:8000

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://interviews.creospan.com
