FROM python:3.9-slim

WORKDIR /app

COPY ./requirements.txt ./

RUN pip install --no-cache-dir -r requirements.txt
RUN apt-get update && apt-get install -y libicu-dev
# Necessary library to parse and read .DOC files
RUN apt-get install antiword

ADD . .

# Internally, this flask server runs on 8002, but gunicorn runs it on 8000
EXPOSE 8002

ENV PYTHONUNBUFFERED=1

CMD ["gunicorn", "-c", "gunicorn-config.py", "main:app"]