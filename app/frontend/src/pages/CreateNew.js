import '../App.css';
import ReactDOM from 'react-dom';
//import JDUploadModal from '../modals/JDUploadModal';
import ResumeUploadModal from '../modals/ResumeUploadModal.js';
import React, {useEffect, useState} from 'react';
import { v4 as uuidv4 } from 'uuid';
import LinkedInModal from '../modals/LinkedInModal.js';
import JobPresetForm from '../modals/JobPresetEditModal.js';
import { Navbar, Container, Image, Form, FormControl, DropdownToggle, DropdownMenu} from 'react-bootstrap';
import CreospanLogo from '../assets/creospanLogo.png';
import NotificationIcon from '../assets/NotificationIcon.png';

import Dropdown from 'react-bootstrap/Dropdown';
import { Sidebar, Menu, MenuItem, SubMenu } from 'react-pro-sidebar';
import ChoosePresetModal from '../modals/ChoosePresetModal.js'
import { getUserImage } from '../index.js';
// Side Bar Icons
import { DashboardIcon } from '../assets/icons/dashboard.tsx';
import { GenerateIcon } from '../assets/icons/generate.tsx';
import { PastInterviewIcon } from '../assets/icons/pastinterview.tsx';
import { JobDescriptionIcon} from '../assets/icons/jobdescription.tsx';
import { MembersIcon } from '../assets/icons/members.tsx';
import { SettingsIcon} from '../assets/icons/settings.tsx';
import { BackArrow } from '../assets/icons/backarrow.tsx';
import { CollapseArrow } from '../assets/icons/collapsearrow.tsx';

const backend_key = process.env.REACT_APP_BACKEND_API;
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL;

const NewInterview = (args) => {
//  const [DescriptionUploaded, SetDescriptionUpload ] = useState(false);
    const [ResumeUploaded, SetResumeUploaded] = useState(false);
    const [JDText, SetJDText] = useState('');
    const [CRText, SetCRText] = useState('');
    const [DDText, SetDDText] = useState('Select Job');
    const [Preset, SetGrabPreset] = useState([null])
    const [DetectPresetOpen, setPresetOpen] = useState(false)
    const [Linkedin_ID, SetLinkedIn] = useState(null);
    const [collapsed, setCollapsed] = useState(false)
    const [Candidate_Name, setCandidateName] = useState("")
    const [ApplicantNO, setApplicantNO] = useState()
    const [JobCode, setJobCode] = useState("")
    const [ClientName, setClientName] = useState("")
    const [ClientManager, setClientManager] = useState("")

    const isAdmin = args.isAdmin()

    const Profile = getUserImage()
    console.log(Profile)


    var sdBar = ""
    if (!isAdmin){
        sdBar = "grey"
    }

    useEffect(() => {
        if(args.presetData.length == 0)
            args.loadPresetsDB()
    }, [args.presetData])
    
   const changeDDValue = (title) => SetDDText(title) 
 

    const handleFileSubmission = () => {
       
        if(( JDText || (DDText != 'Select Job')) && (ResumeUploaded || CRText)){

            console.log(DDText)
            args.setLoading(true);
            args.setAIGenLoading(true);
            args.setDownloadShown(true)
            args.setViewDash(false);
            
            const fileForm = new FormData();

            // Where we put NickName and CEIPAL ID
          
            if(DDText == Preset.Job_Title){
                fileForm.append('jobDescription', Preset.Job_Title + ' - ' + Preset.Job_Description)
            }else{
                DDText ? fileForm.append('jobDescription', DDText) : fileForm.append('jobDescription', JDText);
            }
            ResumeUploaded ? fileForm.append('resumeUpload', ResumeUploaded) : fileForm.append('resumeUpload', CRText);
            if (Linkedin_ID)
                fileForm.append('linkedinProfile', Linkedin_ID)

            let errorData;
        
            fetch(`${API_BASE_URL}/uploadQA`, {
                method: 'POST',
                headers: {
                    'my-custom-header-key': backend_key,
                },
                credentials: 'include',
                body: fileForm,
            })
            .then(async (response) => {
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.message || data.error || `HTTP error! status: ${response.status}`);
                }
                return data;
            })
            .then(async (data) => {
                // Check if data is an error object instead of expected array
                if (data.error) {
                    throw new Error(data.message || data.error);
                }

                // Check if data is an array with expected structure
                if (!Array.isArray(data) || data.length < 7) {
                    throw new Error("Invalid response format from server. Expected array with 7 elements.");
                }

                // Check if the response contains error messages from LLM
                const hasErrorMessages = data.some(item =>
                    typeof item === 'string' &&
                    (item.includes("I'm sorry") || item.includes("I need") || item.includes("Please provide"))
                );

                if (hasErrorMessages) {
                    throw new Error("The AI was unable to process your files. Please ensure you've uploaded a complete resume and job description with sufficient detail.");
                }

                errorData = data;

                // Validate data structure
                if (!data[2] || !Array.isArray(data[2])) {
                    throw new Error("Missing or invalid qualities data in response");
                }
                if (!data[5] || typeof data[5] !== 'object') {
                    throw new Error("Missing or invalid candidate info in response");
                }

                let missing_qualities = '';
                for(let quality of data[2]){
                    missing_qualities += '- ' + quality + '\n\n'
                }
                let general_qs = [];
                let technical_qs = [];
                if(data[0] && Array.isArray(data[0])) {
                    for(let q of data[0]){
                        general_qs.push({...q, notes: ''})
                    }
                }
                if(data[1] && Array.isArray(data[1])) {
                    for(let q of data[1]){
                        technical_qs.push({...q, notes: ''})
                    }
                }
                let MandatoryQS = Preset.MandatoryQS
            
                let saveData = {
                    uuid: uuidv4(),
                    date: getCurrentDate(),
                    Candidate_Name: (data[5] && data[5].Candidate_Name) || 'NOT FOUND' ,
                    Job_Title: (data[5] && data[5].Job_Title) || 'NOT FOUND',
                    Job_Description_Title: (data[5] && data[5].Job_Description_Title) || 'NOT FOUND',
                    Linkedin_ID: (data[5] && data[5].Linkedin_ID) || 'NOT FOUND',
                    general_questions: general_qs || [],
                    technical_questions: technical_qs || [],
                    lacking_qualities: missing_qualities.trim(),
                    strengths: (data[6] && typeof data[6] === 'string') ? data[6].trim() : '',
                    score: data[3] || 0,
                    assessment: data[4] || '',
                    Mandatory_Questions: MandatoryQS,
                    overall_notes: ''
                };
                let linkedinAssess = ''
              //  let linkedinAssess = await fetchLinkedinAssessment(saveData.Linkedin_ID);
                
                saveData = {...saveData, linkedCompare: linkedinAssess}
              //  let imageData = await getImagefromResume()
                saveData = {...saveData, Nick_Name: Candidate_Name, Status: "Pending", Applicant_NO: ApplicantNO, Job_Code: JobCode, Client_Manager: ClientManager, Client_Name: ClientName }
                console.log(saveData)
               // saveData = {...saveData, imageSRC: imageData}
                args.handleSaveCandidate(saveData);
                args.setCandidateInfo(saveData);
                args.setNewInterview(false);
                args.setAIGenLoading(false);
             
                args.setLoading(false);
                
            })
            .catch((error) => {
                console.error('Interview creation error:', error);

                // More specific error messages based on error type
                let errorMessage = "Issue creating Interview. ";
                if (error.message.includes("JSON parsing failed")) {
                    errorMessage += "The AI response could not be processed. This might be due to an overly long resume or complex formatting.";
                } else if (error.message.includes("LLM API")) {
                    errorMessage += "AI service is currently unavailable. Please try again later.";
                } else if (error.message.includes("File processing")) {
                    errorMessage += "Could not process uploaded files. Please check file format and try again.";
                } else if (error.message.includes("Invalid response format")) {
                    errorMessage += "Received unexpected response from server. Please try again.";
                } else {
                    errorMessage += error.message || "Please check the console logs for more details.";
                }

                alert(errorMessage);
                console.log('Error details:', errorData);

                // Reset loading states
                args.setAIGenLoading(false);
                args.setLoading(false);
            });
        }
    }

    const getImagefromResume = async () =>{
        
        const fileForm = new FormData();
        ResumeUploaded ? fileForm.append('resumeUpload', ResumeUploaded) : fileForm.append('resumeUpload', CRText);


        let response = await fetch(`${API_BASE_URL}/getImage`, {
            method: 'POST',
            headers: {
                'my-custom-header-key': backend_key,
            },
            credentials: 'include',
            body: fileForm,
        })
        let data = await response.json();
        
        
        if(data.status == 500){
            alert(data.error.message)
            return ''
        }
        if(data.image){
            const imageSRC = "data:image/png;base64," + data.image;
            args.setImageFound(true)
            return imageSRC
        }
        else
            return ''
        
    }

    const getCurrentDate = () => {
        let date = new Date();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let year = date.getFullYear();
        return `${month}/${day}/${year}`
    }


    
    const fetchLinkedinAssessment = async (linkedinID) => {
      
        if(linkedinID !== 'NOT FOUND'){
            const fileForm = new FormData();
            ResumeUploaded ? fileForm.append('resumeUpload', ResumeUploaded) : fileForm.append('resumeUpload', CRText);
            fileForm.append('linkedinProfile', linkedinID);
    
            let response = await fetch(`${API_BASE_URL}/assessLinkedin`, {
                method: 'POST',
                headers: {
                    'my-custom-header-key': backend_key,
                },
                credentials: 'include',
                body: fileForm,
            })
            let data = await response.json();
            if(data.error){
                alert(data.error.message)
                return ''
            }
            if(data.invalid){
                alert(data.invalid)
                return ''
            }
            return data.assessment
        } else {
            let profileId = await getProfileWithModal();
            if(profileId === ''){
                return ''
            } else {
                const fileForm = new FormData();
                ResumeUploaded ? fileForm.append('resumeUpload', ResumeUploaded) : fileForm.append('resumeUpload', CRText);
                fileForm.append('linkedinProfile', profileId);
                let response = await fetch(`${API_BASE_URL}/assessLinkedin`, {
                    method: 'POST',
                    headers: {
                        'my-custom-header-key': backend_key,
                    },
                    credentials: 'include',
                    body: fileForm,
                })
                let data = await response.json();
                if(data.error){
                    alert(data.error.message)
                    return ''
                }
                if(data.invalid){
                    alert(data.invalid)
                    return ''
                }
                return data.assessment
            }
        }
    }


    function getProfileWithModal() {
        return new Promise((resolve) => {
            const usernameInputModal = (
                <LinkedInModal
                    onClose={(profileId) => {
                        resolve(profileId);
                        closeModal();
                    }}
                />
            );
            ReactDOM.render(usernameInputModal, document.getElementById('modal-root'));
        });
    }

    function closeModal() {
        ReactDOM.unmountComponentAtNode(document.getElementById('modal-root'));
    }

    

    const pastInterviewsPage = () => {
        args.setNewInterview(false)
        args.loadDB((!args.viewDB), (args.viewDash))
    
    }

    const new_Interviews_Page = () =>{
        args.setNewInterview(false)
        args.setNewInterview(true)
       
    } 

    const Dashboard_Page = () =>{
        args.setNewInterview(false)
        args.setJobDescPage(false)
        args.setViewDB(false)
        args.setViewDash(true)
    }
    const Job_Description_Page = () =>{
        if(isAdmin==true){
            args.setNewInterview(false)
            args.setViewDB(false)
            args.setJobDescPage(true)
            args.ADDNEW(true)
            args.setFileClicked(false)
     
        }
    }
    
   
   
    
    return (
        <>  
             <Navbar className="navbar justify-content-between" style={{"height": "60px", padding:"0px"}}>  
               
                    
                    <Navbar.Brand href="#" style={{display:'flex', alignItems:'center'}} > 
                    <Image src={CreospanLogo} roundedCircle width="60" height="60" />
                    </Navbar.Brand>
                   
                    
                   <Container className='d-flex flex-row-reverse'>
                    <Form className='d-flex flex-row-reverse' style={{borderRadius:"50px"}}>
                       <FormControl type='search' id = "search" placeholder='🔍 Search' style={{display:'flex', alignItems:'center', backgroundColor:'#e49d60', border:'#e08537', paddingRight:"210px", color:'white', fontFamily:'Montserrat'}}></FormControl>
                    </Form>
                    </Container>
                  
                  
                <Navbar.Brand href='#'>
                    <Image src={NotificationIcon} roundedCircle width="30" height="30"></Image>
                </Navbar.Brand>
                
                <Navbar.Brand href ='#'>
                    <Image src={Profile} roundedCircle width="40" height="40"></Image>
                </Navbar.Brand>
                 
             </Navbar>
            
            <div className='row main' style={{"marginLeft":'0px',"flex":"1","--bs-gutter-x":"0", fontFamily:'Montserrat'}}>
           
            
            <Sidebar collapsed = {collapsed} style={{width: collapsed ? "50px" : "280px", color:'orange' }}>
            <button onClick={() => setCollapsed(!collapsed)} style={{paddingLeft:'1%', width: collapsed ? "100%" : "35%", border:"none", backgroundColor: "white"}}><CollapseArrow></CollapseArrow></button>

                <Menu iconShape="square" style={{"margin-top":"40%", 'background-color':'white', border: 'white', fontFamily:'Montserrat' }}>
                    <MenuItem onClick={Dashboard_Page} icon={<DashboardIcon color='#E08537'/>} style={{paddingLeft:'1%'}}><div style={{marginLeft:'3%', marginTop:'5%'}}> Dashboard </div> </MenuItem>
                    <MenuItem onClick={new_Interviews_Page} icon={<GenerateIcon color='#E08537'/>} style={{paddingLeft:'3%', marginTop:'1%'}}> Create New </MenuItem>
                    <MenuItem onClick={pastInterviewsPage} icon={<PastInterviewIcon color='#E08537'/>} style={{paddingLeft:'3%'}}> Interviews </MenuItem>
                    <MenuItem onClick={Job_Description_Page} icon={<JobDescriptionIcon color='#E08537'/>} style={{paddingLeft:'3%', marginTop:'2%', backgroundColor:sdBar}}> Job Descriptions </MenuItem>
                    <MenuItem icon={<MembersIcon color='#E08537'/>} style={{paddingLeft:'3%', backgroundColor:'grey'}}> Members </MenuItem>
                    <MenuItem icon={<SettingsIcon color='#E08537'/>} style={{paddingLeft:'3%', backgroundColor:'grey'}}> Settings </MenuItem>
                </Menu>
            </Sidebar>
           
            
            
             

              
               
                        <div className='side'>
                        
                        <div style={{display:'flex', flexDirection:'column'}}>
                        <h1 style={{color: "black", "font-weight": "bold", 'marginLeft': '17%', "font-size": "16px", 'marginTop':'4%', 'marginBottom':'2%', fontFamily:'Montserrat'}}>Candidate Details</h1>
                        
                        <div style={{display:'flex', marginLeft:'17%', marginBottom:'1%' }}>
                            
                            <div style={{display:'flex', flexDirection:'column', marginRight:'7%'}}>
                            <h4 style={{fontSize:'14px', margin:'0'}}>Candidate Name </h4>
                            <textarea className='can-name' onChange={(e) => setCandidateName(e.target.value)}>{ Candidate_Name }</textarea>
                            </div>

                            <div style={{display:'flex', flexDirection:'column', marginRight:'7%'}}>
                            <h4 style={{fontSize:'14px', margin:'0'}}>Applicant NO. </h4>
                            <textarea className='can-name'type ="number" onChange={(e) => setApplicantNO(e.target.value)} required >{ ApplicantNO }</textarea>
                            </div>

                            <div style={{display:'flex', flexDirection:'column', marginRight:'7%'}}>
                            <h4 style={{fontSize:'14px', margin:'0'}}>Job Code</h4>
                            <textarea className='can-name' type ="number" placeholder='Enter Number' required onChange={(e) => setJobCode(e.target.value)}>{ JobCode }</textarea>
                            </div>

                            
                        </div>

                    <div style={{display:'flex', marginLeft:'17%' }}>
                        
                        <div style={{display:'flex', flexDirection:'column', marginRight:'7%'}}>
                            <h4 style={{fontSize:'14px', margin:'0'}}>Client Name </h4>
                            <textarea className='can-name' required onChange={(e) => setClientName(e.target.value)}>{ ClientName }</textarea>
                            </div>

                            <div>
                            <h4 style={{fontSize:'14px', margin:'0'}}>Client Manager </h4>
                            <textarea className='can-name' required onChange={(e) => setClientManager(e.target.value)}>{ ClientManager }</textarea>
                            </div>

                    </div>
                        </div>

                        <h1 style={{color: "black", "font-weight": "bold", 'marginLeft': '17%', "font-size": "14px", 'marginTop':'4%', 'marginBottom':'2%',fontFamily:'Montserrat'}}>Job Description</h1>
                       
                        <Dropdown style={{marginLeft:"17%", width:'50%', 'box-shadow': 'rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px'}}>
                        
                        <DropdownToggle variant ="success" style={{width:"100%", borderRadius:'inherit', color:'black', 'border-block-color': 'white', backgroundColor: 'white', border:'white', paddingLeft:'5%', fontFamily:'Montserrat' }} id='dropdown-basic'>{DDText}</DropdownToggle>
                      
                        <DropdownMenu style={{fontFamily:'Montserrat'}}>
                            <Dropdown.Item as = "button"><div style={{fontFamily:'Montserrat'}} onClick={(e) =>changeDDValue(e.target.textContent)} >EE Hardware Engineer</div></Dropdown.Item>
                            <Dropdown.Item as = "button"><div style={{fontFamily:'Montserrat'}} onClick={(e) =>changeDDValue(e.target.textContent)} >DevOps Automation Engineer</div></Dropdown.Item>
                            <Dropdown.Item as = "button"><div style={{fontFamily:'Montserrat'}} onClick={(e) =>changeDDValue(e.target.textContent)} >Microsoft Azure Subject Matter Expert (SME)</div></Dropdown.Item>
                            <Dropdown.Item as = "button"><div style={{fontFamily:'Montserrat'}} onClick={(e) =>changeDDValue(e.target.textContent)} >Software Engineer - AR/VR Graphics</div></Dropdown.Item>  
                            <Dropdown.Item as = "button" style={{fontFamily:'Montserrat'}} className="btn btn-secondary p-3" data-bs-toggle="modal" data-bs-target="#choosePresetModal" onClick={() => setPresetOpen(true)} >Select Preset</Dropdown.Item>  
                           
                        
                        </DropdownMenu>
                        
                        </Dropdown>
                     
{/*                        <button className='fixed-bottom ' style={{"margin": "20px 0 20px 20px", "width":"80px", border:'none', backgroundColor:'white'}} onClick={() => Dashboard_Page()} ><BackArrow></BackArrow></button>
*/}                        <div>
                            
                            <p style={{color: "#897f7f", 'marginLeft': '17%', "font-size": "14px", 'marginTop':'4%' , marginBottom:'0px', fontFamily:'Montserrat'}}>Paste Job Title and Description Text Below:</p>
                            <textarea className='JDTextArea' value={JDText} onChange={(event) => SetJDText(event.target.value)}></textarea>
                        </div>
                    
                        <div className='row'>
                        <h1 style={{color: "black", "font-weight": "bold", 'marginLeft': '17%', "font-size": "14px", 'marginTop':'3%', 'padding-left':'0.5%'}}>Candidate Resume</h1>
                        
                        
                       
                        
                        <div className='row'>
                           
                            <p style={{color: "#897f7f", 'marginLeft': '17%', "font-size": "14px", marginBottom:'0px', 'padding-left':'inherit', fontFamily:'Montserrat'}}>Paste Resume Text Below:</p>
                            <textarea className='JDTextArea' style={{marginLeft:'18%', width:'50%'}} value={CRText} onChange={(event) => SetCRText(event.target.value)}></textarea>
                        
                        <div style={{width:'13%'}}>
                        <h4 style={{color: '#897f7f', 'marginLeft': '50%', fontSize:'14px', marginTop:'10%', fontFamily:'Montserrat'}}> OR </h4>
                        </div>
                        
                        <div style={{width:'13%', fontFamily:'Montserrat'}}>
                            { ResumeUploaded ? (
                                <button type="button" className="btn btn-secondary p-3" data-bs-toggle="modal" data-bs-target="#selectFileModalResume">{ResumeUploaded.name}</button>
                            ):(
                                <button type="button" className="uploadBtn " data-bs-toggle="modal" data-bs-target="#selectFileModalResume">Upload</button>
                            )}
                        </div>

                        </div>
                        
                       
                </div>
                
                <div className='row' style={{fontFamily:'Montserrat'}}> 
                
                <h1 style={{color: "black", "font-weight": "bold", 'marginLeft': '17%', "font-size": "14px", 'marginTop':'3%', 'padding-left':'0.5%'}}>LinkedIn Profile</h1>
                <p style={{color: "#897f7f", 'marginLeft': '17.5%', "font-size": "14px", marginBottom:'0px', 'padding-left':'inherit'}}>Example: Matt-Drong</p>
                <textarea className='JDTextArea' style={{ width:'49%', marginLeft:'17.5%', height:'102px'}} value={Linkedin_ID} onChange={(event) => SetLinkedIn(event.target.value)}></textarea>
                
               
                </div>

                <div style={{width:'13%', marginLeft:'17%', marginTop:'3%'}}>
                    <button style={{'background-color':'#e08537', 'border':'#e08537'}} type="button" className='uploadBtn' onClick={handleFileSubmission}>Submit</button>
                </div>

                </div>
               

               

                </div>
            

            <ChoosePresetModal SetGrabPreset = {SetGrabPreset} presetData ={args.presetData} isOpen={DetectPresetOpen} setPresetOpen ={setPresetOpen} SetDDText ={SetDDText} />
            <ResumeUploadModal SetResumeUploaded={SetResumeUploaded}/>
        </>
    )

}

export default NewInterview;