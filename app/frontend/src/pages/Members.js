import "../App.css"
import {React, useState, useEffect} from "react"
import { Navbar, Container, Image, Form, FormControl, DropdownToggle, DropdownMenu} from 'react-bootstrap';
import { getUserImage, getUserName } from '../index.js';
import { Sidebar, Menu, MenuItem, SubMenu } from 'react-pro-sidebar';

const Members = (args) =>{
    const userImageURL =  getUserImage()
    const isAdmin = args.isAdmin()
    const user = getUserName()

    var sdBar = ""
    if (!isAdmin){
        sdBar = "grey"
    }
    
    var addButtonColor = "grey"
    var fileButtonColor = "grey"

    const ADDNEWClicked = () =>{
        console.log('Test')
    }

    const FileCollectionClicked = () =>{
        console.log('Test')

    }

    const generateInterviewPage = () =>{

        console.log("test")
    }

    const pastInterviewsPage = () =>{

        console.log("test")
    }

    const JobDescriptionPage = () =>{

        console.log("test")
    }

    const Dashboard_Page = () =>{
        console.log("test")
    }

    return (
        <>
          <div className='row main' style={{"marginLeft":'0px',"flex":"1","--bs-gutter-x":"0", alignContent:'flex-start', overflow:'hidden'}}>
                   
                  
                   <Sidebar id='dash' style={{"width":"280px",'color':'white', overflow:'auto', backgroundColor:'#e08537 !important', height:'100vh',}} >
        
                            <div>
                            <img src= {userImageURL} width='81px' style={{marginTop:'4%', marginLeft:'30%'}}></img>
                            <h5 style={{marginLeft:'18%', marginTop:'5%', fontFamily:'Montserrat'}}>{user}</h5>
        
                            </div>
        
        
                        <Menu iconShape="square" style={{ border: '#e08537', backgroundColor:'#e08537' , fontFamily:'Montserrat'}} menuItemStyles={{button: {'&:hover': {
                        backgroundColor: '#C6742F !important', //hover color
                        color: '#ffff !important' //text color
                        },},}} >
                           
                        <MenuItem className = "menu"  style={{paddingLeft:'28%'}}>Edit Profile</MenuItem>
                        <MenuItem className = "menu"  style={{paddingLeft:'30%',}}>Logout</MenuItem>
                        <MenuItem style={{paddingLeft:'1%', backgroundColor:"#e08537"}}>___________________________</MenuItem>
                        <MenuItem className = "menu"  onClick = {Dashboard_Page} style={{paddingLeft:'1%'}}><div style={{marginLeft:'3%', marginTop:'5%'}}> Dashboard </div> </MenuItem>
                        <MenuItem className = "menu"  onClick={generateInterviewPage}  style={{paddingLeft:'3%', marginTop:'1%'}}> Create New </MenuItem>
                        <MenuItem className = "menu"  onClick={pastInterviewsPage} style={{paddingLeft:'3%'}}> Interviews </MenuItem>
                        <MenuItem className = "menu"  onClick={JobDescriptionPage} style={{paddingLeft:'3%', marginTop:'2%', backgroundColor: sdBar}}> Job Descriptions </MenuItem>
                        <MenuItem className = "menu"  style={{paddingLeft:'3%', }}> Members </MenuItem>
                        <MenuItem className = "menu"  style={{paddingLeft:'3%', }}> Settings </MenuItem>
                    </Menu>
                   </Sidebar>
                 
                     <div className='main' style={{overflow: "initial", flex:1}}>
                     <div className='w-100 file-upload-info d-flex' style={{backgroundColor: 'black', height:'50px', marginBottom:'0px !important', paddingLeft:'3%'}}><h2 style={{marginTop:'1.2%', fontSize:'17px', color:'white', fontFamily:'Montserrat'}}>Members</h2></div>
                     <div className='w-100 file-upload-info d-flex css-dip3t8' style={{backgroundColor: 'white', height:'50px', marginBottom:'0px !important', paddingLeft:'3%'}} >
                     <div>
                     <button onClick={ADDNEWClicked} id='add' style={{fontSize:'13px',backgroundColor:'white', color: addButtonColor, border:'white', marginRight:'2%', height:'100%', width:'103%', fontFamily:'Montserrat'}}>Add New</button>
                     </div>
                     <div style={{marginLeft:'5%'}}>
                     <button onClick={FileCollectionClicked} id='file' style={{color: fileButtonColor, fontSize:'13px',backgroundColor:'white', border:'white', marginRight:'2%', height:'100%', width:'100%', fontFamily:'Montserrat'}}>Members List</button>
                     </div>
                     
                 
                 
                  </div>
                  </div>
                  </div>
        </>
    )
}
export default Members;