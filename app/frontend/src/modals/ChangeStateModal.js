import '../App.css';
import React, {useEffect, useState} from 'react';
import {Modal, Button, Form} from 'react-bootstrap';
const backend_key = process.env.REACT_APP_BACKEND_API;

/**
 * DeleteInterviewModal component.
 * 
 * @param {Object} args - The arguments object.
 * @param {boolean} args.setLoading - Function to set the loading state.
 * @param {string} args.deleteUuid - The UUID of the Preset to be deleted.
 * @param {boolean} args.loadUNPresets - Function to load the UNPresets database.
 * @param {boolean} args.loadPresetsDB - Function to load the JDPresets database.

 * @param {string} args.deleteDesc - The description of the job preset.
 * @param {string} args.deleteJob - The job name for the preset
 * @param {string} args.setLoadJD - The JD File Page 
 * @returns {JSX.Element} The DeletePresetModal component.
 */
const AssessPresetModal = (args) => {
    const [data, setData] = useState(args.presetData);
    const [JobTitle, SetJobTitle] = useState(data?.Job_Title || "");
    const [JobDescription, SetJobDescription] = useState(data?.Job_Description || "");
    const [Client_Manager, setClientManager] = useState(data?.Client_Manager || "")
    const [Client_Name, setClientName] = useState(data?.Client_Name || "")
    const [Created_By, SetCreatedBy] = useState(data?.creator || "")
    const [Uuid, setUUid] = useState(data?.JD_Uuid || "")
    const [date, setDate] = useState(data?.date || "")
    const [Current_State, setCurrentState] = useState(data?.Status || "")
    const [Open, setOpen] = useState(false)

    const changedState = args.newState
 //   console.log(changedState)


    useEffect(() => {
        if(args.presetData.length != 0 && args.isOpen==true){
               setData(args.presetData)

            if(args.isOpen ==true)
            {
                setOpen(true)
                SetJobTitle(data?.Job_Title || "")
                SetJobDescription(data?.Job_Description || "");
                setClientManager(data?.Client_Manager || "")
                setClientName(data?.Client_Name || "")
                SetCreatedBy(data?.creator || "")
                setUUid(data?.JD_Uuid || "")
                setDate(data?.date || "")
                setCurrentState(data?.Status || "")
                
            }
        }

    }, [args.presetData, data])


    


    const savePreset = async () =>
    {
        if(Current_State === 'Archived'){

            if ((JobDescription && JobTitle && Client_Manager && Client_Name && Created_By && changedState) != '')
                {
                     let saveData = {
                        JD_Uuid : Uuid,
                        Job_Title: JobTitle,
                        Job_Description : JobDescription,
                        date : date,
                        creator: Created_By,
                        Client_Name: Client_Name,
                        Client_Manager: Client_Manager,
                        Status: changedState
                      
                     };
         
                args.handleSavePreset(saveData)
                args.setJobDescPage(false)
                removePreset(Uuid)
                args.setSelectedPreset([])
                await args.loadARPresets()
                
                    onClose()
                 }
                    
                
        }
        else 
        {
        if(changedState === 'Archived')
        {
            if ((JobDescription && JobTitle && Client_Manager && Client_Name && Created_By && changedState) != '')
                {
                     let saveData = {
                        JD_Uuid : Uuid,
                        Job_Title: JobTitle,
                        Job_Description : JobDescription,
                        date : date,
                        creator: Created_By,
                        Client_Name: Client_Name,
                        Client_Manager: Client_Manager,
                        Status: changedState
                      
                     };
         
                args.handleSaveAR(saveData)
                console.log("UUID to be deleted is : ", Uuid)
                removePreset(Uuid)
                args.setJobDescPage(false)
              
                args.setSelectedPreset([])
                await args.loadPresetsDB()
                
                    onClose()
                 }
            
        }
        
        else{

        
        if ((JobDescription && JobTitle && Client_Manager && Client_Name && Created_By && changedState) != '')
            {
                 let saveData = {
                    JD_Uuid : Uuid,
                    Job_Title: JobTitle,
                    Job_Description : JobDescription,
                    date : date,
                    creator: Created_By,
                    Client_Name: Client_Name,
                    Client_Manager: Client_Manager,
                    Status: changedState
                  
                 };
     
            args.handleSavePreset(saveData)
            args.setJobDescPage(false)
          
            args.setSelectedPreset([])
            await args.loadPresetsDB()
            
                onClose()
             }
                
            }
        }
    }

   const removePreset = (uuid) =>{
        var route = ''
        if(args.archiveClicked == true)
            route = `/deleteAR`
        else
            route = `/deleteJD`
        
        fetch(`${route}`, {
            method: 'POST',
            headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include',
            body: JSON.stringify({uuid: uuid}),
        })
        .then(response => {
            return response.json();
        })
        .then(data => {
            console.log(data);
            //args.loadPresetsDB()
         //   alert('Preset Log Deleted.')
           
        })
        .catch(error => {
            alert('Error Deleting Preset.')
            console.error(`${error}`);
        });
    

   }
   
    const onClose = async () =>{
        args.setSelectedPreset([])
        args.setJobDescPage(false)
        args.setIsOpen(false)
        setOpen(false)
        if(args.archiveClicked == true)
            await args.loadARPresets()
        else
            await args.loadPresetsDB()
        
        args.setJobDescPage(true)
     
    
    }

    return (
        <>
            <Modal show = {Open} id='changeStateModal' tabIndex={-1}>
            <Modal.Header closeButton onClick={onClose}>
                <Modal.Title>Change Current Status to {changedState} ?</Modal.Title>
            </Modal.Header>
                <Modal.Body style={{fontFamily:'Montserrat'}}>
                    
                            <>
                            <h5>Current Preset:</h5>
                            <p>
                                {`Job Title: ${JobTitle}`} 
                            </p> 
                            <p>
                                {`Job Description: ${JobDescription}`} 
                            </p> 
                            <p>
                                {`Client Name: ${Client_Name}`} 
                            </p> 
                            <p>
                                {`Client Manager: ${Client_Manager}`} 
                            </p> 
                            <p>
                                THIS CANNOT BE UNDONE!
                            </p>
                            </>
                       
                </Modal.Body>
                <Modal.Footer>
                    <Button variant ='secondary' onClick={() => onClose()}>Cancel</Button>
                    <Button style={{backgroundColor:'green'}} variant = 'primary' onClick={() => savePreset()}>Save</Button>
                </Modal.Footer>      
            </Modal>
        </>
    )
}

export default AssessPresetModal;