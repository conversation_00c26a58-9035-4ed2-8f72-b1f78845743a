import '../App.css';
import React, {useEffect, useState} from 'react';
import {Modal, Button, Form} from 'react-bootstrap';
const backend_key = process.env.REACT_APP_BACKEND_API;

/**
 * DeleteInterviewModal component.
 * 
 * @param {Object} args - The arguments object.
 * @param {boolean} args.setLoading - Function to set the loading state.
 * @param {string} args.deleteUuid - The UUID of the Preset to be deleted.
 * @param {boolean} args.loadUNPresets - Function to load the UNPresets database.
 * @param {boolean} args.loadPresetsDB - Function to load the JDPresets database.

 * @param {string} args.deleteDesc - The description of the job preset.
 * @param {string} args.deleteJob - The job name for the preset
 * @param {string} args.setLoadJD - The JD File Page 
 * @returns {JSX.Element} The DeletePresetModal component.
 */
const ApproveModal = ({onCloseModal, args}) => {
    const [data, setData] = useState(args?.saveData);
    const [Current_State, setCurrentState] = useState(data?.Status || "")

 //   console.log(changedState)
    let newState = ''
    if(Current_State === "Pending") {
        newState = "Approved"
    } 
    else if(Current_State === "Approved") {
        newState = "Completed"
    }

    

    useEffect(() => {
        console.log("Opened: ", args?.isOpen)
        if(args?.saveData?.length != 0 && args?.isOpen==true){
            setData(args?.presetData)

            if(args?.isOpen ==true)
            {
                setCurrentState(data?.Status || "")
            }
        }

    }, [args?.presetData, data])


    

    const savePreset = async () => {
        if(Current_State === "Pending") {
            console.log("CAN INFO: ", args.candidateInfo)
            let saveData = {
                ...args.saveData,
                Status: "Approved"
            }
            console.log("saveData: ", saveData)
            console.log("InterviewState: ", Current_State)
            args.handleSaveCandidate(saveData)
            redirectModal()
           
        }
        else if(Current_State === "Approved") {
                let saveData = {
                    ...args.saveData,
                    Status: "Completed"
                }
            console.log("saveData: ", saveData)
            console.log("InterviewState: ", Current_State)
            args.handleSaveCandidate(saveData)
            redirectModal()
           
    }
    
    
}
   const redirectModal = () => {
    onCloseModal()
    args.setIsOpen(false)
   }
    const onClose = async () =>{
        args.setIsOpen(false)
    }

    return (
        <>
            <Modal show = {args.isOpen} id='approvalModal' tabIndex={-1} >
            <Modal.Header closeButton onClick={onClose}>
                <Modal.Title>Change Interview Status to {newState} ?</Modal.Title>
            </Modal.Header>
                <Modal.Body style={{fontFamily:'Montserrat'}}>
                            <>
                            <h2>
                                THIS CANNOT BE UNDONE!
                            </h2>
                            </>
                       
                </Modal.Body>
                <Modal.Footer>
                    <Button variant ='secondary' onClick={() => onClose()}>Cancel</Button>
                    <Button style={{backgroundColor:'green'}} variant = 'primary' onClick={() => savePreset()}>Save</Button>
                </Modal.Footer>      
            </Modal>
        </>
    )
}

export default ApproveModal;