import  React, { useState,  useEffect } from 'react';
import {Modal, Button, Form} from 'react-bootstrap';

/**

@param {Object} args.presetData - The component props.
@param {Function} args.handleSavePreset - The function to be called when the modal is closed.
@returns {JSX.Element} The JobPresetModal component.

**/


const MandatoryQuestionModal = (args) =>{

    const [data, setData] = useState(args?.presetData || "");
    const [MandatoryQS, setMandatoryQS] = useState(args?.MandatoryQS || [])
   

    useEffect(() =>{        
            console.log("MandData:", args.MandatoryQS) 
            setMandatoryQS(args?.MandatoryQS || [])
    } , [args.MandatoryQS])



    function handleInputArray(index, field, value) {
        setMandatoryQS(prevState => {
            const newState = [...prevState];
                newState[index] = newState[index] || { question: "", lookFor: "" };
    
            newState[index] = {
                ...newState[index], 
                [field]: value
            };
            console.log(newState)
            return newState;
        });
    }

    async function handleSave ()  {
    
        if (MandatoryQS)
        {
            args.setMandatoryQS(MandatoryQS)
      //  args.setJobDescPage(false)
            args.setSelectedPreset([])
        
            onClose()
         }   
        
    }
    
    
    const onClose = async () =>{
       
      ///  args.setJobDescPage(false)
        args.setSelectedPreset([])
        args.setIsOpen(false)
        await args.loadPresetsDB()
       
       
    }

    return (
        <>
        
        <Modal style={{"--bs-modal-width": "1000px"}} show = {args.isOpen} id='mandQuestionModal' tabIndex={-1}>
            <Modal.Header closeButton onClick={onClose}>
                <Modal.Title>Modify Mandatory Questions</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form>
                    <Form.Group className='mb-3'>
                    
                    
                    <Form.Label>Mandatory Question 1</Form.Label>
                    <Form.Control key = {0} as ="textarea" rows={3} value={MandatoryQS[0]?.question}  style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(0, "question", e.target.value)}></Form.Control>
                    <Form.Label>Look For:</Form.Label>
                    <Form.Control key = {0} as ="textarea" rows={3} value={MandatoryQS[0]?.lookFor} style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(0, "lookFor", e.target.value)}></Form.Control>
                        
                    <Form.Label>Mandatory Question 2</Form.Label>
                    <Form.Control key = {1} as ="textarea" rows={3} value={MandatoryQS[1]?.question}  style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(1, "question", e.target.value)}></Form.Control>
                    <Form.Label>Look For:</Form.Label>
                    <Form.Control key = {1} as ="textarea" rows={3} value={MandatoryQS[1]?.lookFor} style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(1, "lookFor", e.target.value)}></Form.Control>
                    
                    
                    <Form.Label>Mandatory Question 3</Form.Label>
                    <Form.Control key = {2} as ="textarea" rows={3} value={MandatoryQS[2]?.question}  style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(2, "question", e.target.value)}></Form.Control>
                    <Form.Label>Look For:</Form.Label>
                    <Form.Control key = {2} as ="textarea" rows={3} value={MandatoryQS[2]?.lookFor} style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(2, "lookFor", e.target.value)}></Form.Control>
                        
                    <Form.Label>Mandatory Question 4</Form.Label>
                    <Form.Control key = {3} as ="textarea" rows={3} value={MandatoryQS[3]?.question}  style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(3, "question", e.target.value)}></Form.Control>
                    <Form.Label>Look For:</Form.Label>
                    <Form.Control key = {3} as ="textarea" rows={3} value={MandatoryQS[3]?.lookFor} style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(3, "lookFor", e.target.value)}></Form.Control>

                    </Form.Group>
                </Form>
            </Modal.Body>
            <Modal.Footer>
                <Button variant ='secondary' onClick={() => onClose()}>Close</Button>
                <Button style={{backgroundColor:'green', border:'green'}} variant = 'primary' onClick={() => handleSave()}>Save Changes</Button>
               

            </Modal.Footer>

        </Modal>

    </>
    );
}


    
export default MandatoryQuestionModal