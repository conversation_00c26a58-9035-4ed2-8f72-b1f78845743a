import './App.css';
import React, {useState} from 'react';
import DisplayInterview from './pages/DisplayInterview';
import JobDescriptionPage from './pages/JobDescription.js';
import InterviewDB from './pages/Interview.js';
import NewInterview from './pages/CreateNew.js';
import Loading from './pages/Loading';
import Dashboard from './pages/Dashboard';
import Members from './pages/Members.js';
import { getUserRole, getUserName, getUserImage } from './index.js';

const backend_key = process.env.REACT_APP_BACKEND_API;
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL;

/**
 * Component for managing the InterviewGPT functionality.
 *
 * @returns {JSX.Element} The InterviewGPT component.
 */
const InterviewGPT = ({user, msal}) => {
	/**
	 * All these state variables are used in AT LEAST 2 jsx components and so they are defined here and passed through args
	 * At a later point, the code should be split up further. Currently, nearly all jsx "components" are just entire pages. 
	 * 
	 * Various boolean state variables to control the flow of the application (which page to display when a specific event occurs).
	 * Can be tricky to follow so may be refactored later.
	*/
	
	const [loading, setLoading] = useState(false);
	const [AIGenLoading, setAIGenLoading] = useState(false);
	const [viewDB, setViewDB] = useState(false);
	const [newInterview, setNewInterview] = useState(false);
	const [viewDash, setDashboard] = useState(true)
	const [JobDescPage, setJobDescPage] = useState(false)
	const [MembersPage, setMembersPage] = useState(false)
	
	// A single interview which is passed to DisplayInterview component to display the generated data and notes page
	const [candidateInfo, setCandidateInfo] = useState();

	// State variable containing all of the interviews previously generated, gotten from the db to be displayed in the db user interface page 
	const [interviewsData, setInterviewsData] = useState([]);
	const [presetData, setPresetData] = useState([])
	const [ARData, setARData] = useState([]) 
	const [showDownloadButton, setDownloadShown] = useState(false)
	const [imageFound, setImageFound] = useState(false)
	const [powerAdmin, setPowerAdmin] = useState(false)
	const [selectedPreset, setSelectedPreset] = useState([])

	
	function checkRole(){
		const userRole = getUserRole()
		console.log(userRole)
		 if(userRole === "admin"){
			setPowerAdmin(true)
			return true
		}
		else{
			return false
		}
	}

	function logoutRedirect() {
		// In development mode, msal might be null
		if (msal && msal.logoutRedirect) {
			msal.logoutRedirect()
		} else {
			// In development mode, just show a message instead of reloading
			console.log('Logout requested in development mode - MSAL not available');
		}
	}

	async function loadDB(viewDB, dashboard){
		return new Promise((resolve, reject) => {
        setLoading(true);
		console.log(viewDB, dashboard)
		setDashboard(!dashboard)

        fetch(`${API_BASE_URL}/getAll`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include'
		})
        .then((response) => response.json())
        .then((data) => {
			
            setInterviewsData(data || []);
			
			console.log(data || [])
            setViewDB(viewDB);
			setDashboard(dashboard)
            setLoading(false);
			console.log("Database Loaded! (:")
			
			
			setTimeout(() =>{
				resolve(data || [])
				
				
			}, 2000)
			
			
		})
			
		
		})
        .catch((error) => {
			alert("Trouble retrieving Data. Server May be down. Check Logs");
            console.error('Error:', error);
        });
	
    }

	async function loadPresetsDB(){
		return new Promise((resolve, reject) => {
	
        setLoading(true);
    
        fetch(`${API_BASE_URL}/getAllPresets`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include'
		})
        .then((response) => response.json())
        .then((data) => {
			
            setPresetData(data || []);
			
			console.log(data || [])
	
            setLoading(false);
		
			
			
			console.log(" JD Database Loaded! O:")
			
			
			setTimeout(() =>{
				resolve(data || [])
		
				
			}, 2000)
			
			
		})
			
		
		})
        .catch((error) => {
			alert("Trouble retrieving JD-Data. Server May be down. Check Logs");
            console.error('Error:', error);
        });
	
    }


	async function loadAR_DB(){
		return new Promise((resolve, reject) => {
        setLoading(true);
			console.log('RAN!')
        fetch(`${API_BASE_URL}/getAllARPresets`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include',

		})
        .then((response) => response.json())
        .then((data) => {
			
            setARData(data || []);
			
			console.log(data || [])

            setLoading(false);
		
			
			
			console.log(" AR Database Loaded! /:")
			
			
			setTimeout(() =>{
				resolve(data || [])
		
				
			}, 2000)
			
			
		})
			
		
		})
        .catch((error) => {
			alert("Trouble retrieving JD-Data. Server May be down. Check Logs");
            console.error('Error:', error);
        });
	
    }

	

	const handleSaveCandidate = (saveData) => {
		fetch(`${API_BASE_URL}/saveCandidate`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include',
			body: JSON.stringify({...saveData, user:user}),
		})
		.then((response) => response.json())
		.then((data) => {
			console.log(data);
		})
		.catch((error) => {
			alert("Trouble Saving Data. Check Logs.")
			console.error('Error:', error);
		});
    }

	const handleSavePreset = (saveData) => {
		console.log(saveData)
		fetch(`${API_BASE_URL}/saveJD`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include',
			body: JSON.stringify({...saveData}),
			
		})
		.then((response) => response.json())
		.then((data) => {
			console.log(data);
		})
		.catch((error) => {
			alert("Trouble Saving Data. Check Logs.")
			console.error('Error:', error);
		});
    }

	const handleSaveAR = (saveData) => {
		console.log(saveData)
		fetch(`${API_BASE_URL}/saveAR`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'my-custom-header-key': backend_key,
			},
			credentials: 'include',
			body: JSON.stringify({...saveData}),
			
		})
		.then((response) => response.json())
		.then((data) => {
			console.log(data);
		})
		.catch((error) => {
			alert("Trouble Saving Data. Check Logs.")
			console.error('Error:', error);
		});
    }

	return (
		<>
			{!viewDash ? (
				<>
					{loading ? (
						<Loading AIGenLoading={AIGenLoading}/>
					): (
						<DisplayInterview candidateInfo={candidateInfo} setCandidateInfo={setCandidateInfo} handleSaveCandidate={handleSaveCandidate} setViewDash = {setDashboard} setNewInterview={setNewInterview} loadDB={loadDB} setViewDB={setViewDB} interviewsData={interviewsData} showDownloadButton = {showDownloadButton} setDownloadShown = {setDownloadShown}  viewDB={viewDB} viewDash={viewDash} setLoading={setLoading} isLoading ={loading} imageFound={imageFound} setJobDescPage = {setJobDescPage} />
					)}
				</>
			): (
				<>
					{newInterview ? (
						<>
							
							<NewInterview setAIGenLoading={setAIGenLoading} setLoading={setLoading} setCandidateInfo={setCandidateInfo} setNewInterview={setNewInterview} setViewDB={setViewDB} loadDB={loadDB} handleSaveCandidate={handleSaveCandidate} setViewDash = {setDashboard} setDownloadShown = {setDownloadShown}  viewDB={viewDB} viewDash={viewDash} setImageFound={setImageFound} setJobDescPage = {setJobDescPage} isAdmin ={checkRole} presetData ={presetData} loadPresetsDB = {loadPresetsDB} setMembersPage ={setMembersPage} logoutRedirect ={logoutRedirect} />
						</>
					):(
						viewDB ? (
							<>
								<InterviewDB loadDB={loadDB} interviewsData={interviewsData} setLoading={setLoading} setViewDB={setViewDB} viewDash={viewDash} viewDB ={viewDB} setCandidateInfo={setCandidateInfo}  setViewDash = {setDashboard } setJobDescPage = {setJobDescPage} powerAdmin ={powerAdmin} setNewInterview={setNewInterview} logoutRedirect ={logoutRedirect}/>
							</>
						
						):(
						JobDescPage ? (
							<>
							<JobDescriptionPage setViewDB={setViewDB} viewDB={viewDB} viewDash={viewDash} loadDB={loadDB}  setNewInterview={setNewInterview} setViewDash = {setDashboard} setDownloadShown = {setDownloadShown} isAdmin ={checkRole} setJobDescPage = {setJobDescPage} presetData ={presetData}   loadPresetsDB = {loadPresetsDB} loadAR_DB ={loadAR_DB} handleSavePreset = {handleSavePreset} handleSaveAR ={handleSaveAR} setLoading={setLoading}  setPresetData={setPresetData}  setLoadJD ={setJobDescPage} powerAdmin ={powerAdmin}   ARData = {ARData} setARData = {setARData} selectedPreset ={selectedPreset} setSelectedPreset ={setSelectedPreset} setMembersPage ={setMembersPage} logoutRedirect ={logoutRedirect}/>
							</>
						): MembersPage ? (
							<>
							<Members setViewDB={setViewDB} viewDB={viewDB} viewDash={viewDash} setNewInterview={setNewInterview} setViewDash = {setDashboard} isAdmin ={checkRole} setJobDescPage = {setJobDescPage} setLoading={setLoading}  setLoadJD ={setJobDescPage} powerAdmin ={powerAdmin} setMembersPage ={setMembersPage} logoutRedirect ={logoutRedirect}/>
							</>
						):(
							<Dashboard setViewDB={setViewDB} viewDB={viewDB} viewDash={viewDash} loadDB={loadDB}  setNewInterview={setNewInterview} setViewDash = {setDashboard} setDownloadShown = {setDownloadShown} isAdmin ={checkRole} setJobDescPage = {setJobDescPage} loadPresetsDB = {loadPresetsDB} setMembersPage ={setMembersPage} logoutRedirect ={logoutRedirect}   />

						)
					) 
					)
					
					}
				</>
			)}
			<div id='modal-root'></div>
		</>
	);
}

export default InterviewGPT;