import { LogLevel } from "@azure/msal-browser";

// AZURE APP REGISTRATION CONFIGURATION 
export const msalConfig = {
    auth: {
        clientId: '04efb143-adb9-43eb-ab40-e8bfca657adb', // This is the ONLY mandatory field that you need to supply.
        authority: 'https://login.microsoftonline.com/creospan.com',

        redirectUri: 'http://localhost:3000', // DEVELOPEMENT
        postLogoutRedirectUri: 'http://localhost:3000', //DEVELOPEMENT

        // redirectUri: 'https://interviews.creospan.com', // PRODUCTION
        // postLogoutRedirectUri: 'https://interviews.creospan.com', // PRODUCTION
        
        navigateToLoginRequestUrl: true, // Navigate back to the original request location to maintain URL state
    },
    cache: {
        cacheLocation: 'localStorage', // Using localStorage to maintain auth state across page refreshes and navigation
        storeAuthStateInCookie: true, // Store auth state in cookies to help with navigation issues
    },
    system: {
        loggerOptions: {
            loggerCallback: (level, message, containsPii) => {
                if (containsPii) {
                    return;
                }
                switch (level) {
                    case LogLevel.Error:
                        console.error(message);
                        return;
                    case LogLevel.Info:
                        console.info(message);
                        return;
                    case LogLevel.Verbose:
                        console.debug(message);
                        return;
                    case LogLevel.Warning:
                        console.warn(message);
                        return;
                    default:
                        return;
                }
            },
        },
    },
};

/**
 * Scopes you add here will be prompted for user consent during sign-in.
 * By default, MSAL.js will add OIDC scopes (openid, profile, email) to any login request.
 * For more information about OIDC scopes, visit: 
 * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-permissions-and-consent#openid-connect-scopes
 */
export const loginRequest = {
    scopes: ['user.read'],
};