/**
 * API constants and configuration for the frontend application.
 */

// Base API URL from environment variables
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8002';

// API Headers
export const API_HEADERS = {
  'Content-Type': 'application/json',
  'X-API-Key': process.env.REACT_APP_BACKEND_API || '',
};

// API Endpoints
export const API_ENDPOINTS = {
  // Candidate endpoints
  CANDIDATES: {
    GET_ALL: '/api/candidates/getAll',
    GET_CANDIDATE: '/api/candidates/getCandidate',
    ADD_CANDIDATE: '/api/candidates/addCandidate',
    UPDATE_CANDIDATE: '/api/candidates/updateCandidate',
    DELETE_CANDIDATE: '/api/candidates/deleteCandidate',
  },
  
  // Interview endpoints
  INTERVIEW: {
    GENERATE_QUESTIONS: '/api/interview/generateQuestions',
    REGENERATE_TECHNICAL: '/api/interview/regenerateTechnical',
    REG<PERSON>ERATE_BEHAVIORAL: '/api/interview/regenerateBehavioral',
  },
  
  // Legacy endpoints (to be migrated)
  LEGACY: {
    GET_ALL: '/getAll',
    GET_CANDIDATE: '/getCandidate',
    ADD_CANDIDATE: '/addCandidate',
    UPDATE_CANDIDATE: '/updateCandidate',
    DELETE_CANDIDATE: '/deleteCandidate',
    GENERATE_QUESTIONS: '/generateQuestions',
    REGENERATE_TECHNICAL: '/regenerateTechnical',
    REGENERATE_BEHAVIORAL: '/regenerateBehavioral',
    GET_ALL_JD: '/getAllJD',
    GET_JD: '/getJD',
    ADD_JD: '/addJD',
    UPDATE_JD: '/updateJD',
    DELETE_JD: '/deleteJD',
    GET_ALL_ARCHIVED: '/getAllArchived',
    GET_ARCHIVED_CANDIDATE: '/getArchivedCandidate',
    ADD_ARCHIVED_CANDIDATE: '/addArchivedCandidate',
    DELETE_ARCHIVED_CANDIDATE: '/deleteArchivedCandidate',
  }
};

// File upload configuration
export const FILE_CONFIG = {
  MAX_SIZE: parseInt(process.env.REACT_APP_MAX_FILE_SIZE) || 5368709120, // 5GB
  ALLOWED_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
  ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt'],
};

// Application configuration
export const APP_CONFIG = {
  DEBUG: process.env.REACT_APP_DEBUG === 'true',
  ENVIRONMENT: process.env.REACT_APP_ENV || 'development',
};
