/* For ease all css classes are defined here.
   May be spread out later if the app grows in size
*/

html, .App {
  background-color: var(--fullwhite);
  padding: 0;
  margin: 0;
  font-family: <PERSON><PERSON>, "Americane", "Neue", Helvetica, sans-serif, "Roboto Slab", "Montserrat";
}

:root {
  --gray: #353541;
  --orange: #F18544;
  --black: #040405d6;
  --lightblack: #7f7f8fd6;
  --white: antiquewhite;
  --fullwhite: #ffff;
}

.form-control:before {
  background: url("data:image/svg+xml,%3Csvg%20fill%3D%22%23000000%22%20height%3D%22182px%22%20width%3D%22182px%22%20version%3D%221.1%22%20id%3D%22Capa_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20viewBox%3D%220%200%20488.40%20488.40%22%20xml%3Aspace%3D%22preserve%22%20stroke%3D%22%23000000%22%20stroke-width%3D%220.004884%22%3E%3Cg%20id%3D%22SVGRepo_bgCarrier%22%20stroke-width%3D%220%22%3E%3C%2Fg%3E%3Cg%20id%3D%22SVGRepo_tracerCarrier%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke%3D%22%23CCCCCC%22%20stroke-width%3D%221.9535999999999998%22%3E%3C%2Fg%3E%3Cg%20id%3D%22SVGRepo_iconCarrier%22%3E%3Cg%3E%3Cg%3E%3Cpath%20d%3D%22M0%2C203.25c0%2C112.1%2C91.2%2C203.2%2C203.2%2C203.2c51.6%2C0%2C98.8%2C19.4%2C134.7%2C51.2l129.5%2C129.5c2.4%2C2.4%2C5.5%2C3.6%2C8.7%2C3.6%20s6.3%2C-1.2%2C8.7%2C-3.6c4.8%2C4.8%2C4.8%2C12.5%2C0%2C17.3l-129.6%2C129.5c31.8%2C35.9%2C51.2%2C83%2C51.2%2C134.7c0%2C112.1%2C91.2%2C203.2%2C203.2%2C203.2%20S0%2C91.15%2C0%2C203.25z%20M381.9%2C203.25c0%2C98.5%2C-80.2%2C178.7%2C-178.7%2C178.7s-178.7%2C-80.2%2C-178.7%2C-178.7s80.2%2C-178.7%2C178.7%2C-178.7%20S381.9%2C104.65%2C381.9%2C203.25z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");


}

.blob-title {
  font-size: x-large;
  color: rgb(234, 228, 220);
  font-weight: bolder;
}

.convo-section {
  margin: 5rem 0 0 0;
  width: 80vw;
  height: calc(100vh - 15rem); 
  overflow: scroll;
}

.convo-bubble {
  width: 70%;
  border-radius: 20px;
  min-height: 8vh;
}

.convo-content {
  margin: 1%;
}

.customCard {
  height: 60vh;
  width: 30vw;
  display: flex;
  flex-direction: column;
  border-radius: 10%;
  background-color: #e08537;
}

.cardIcon {
  margin: auto;
  width: 50%;
  padding: 10px;
}

.cardInfo {
  margin: auto;
  text-align: center;
}

.customCard:hover {
  cursor: pointer;
  background-color: rgb(255, 177, 87);
}
.dropdownZindex{
  z-index: 1050;
  position: relative;
}

.file-upload-info {
  background-color: var(--black);
  color: var(--white);
}



.interview-bubble {
  width: 100%;
  border-radius: 20px;
  padding: 10px;
  border-bottom-right-radius: 0;
}

.JDTextArea {
  width: 50%;
  height: 120px;
  background-color: #f5f5f5;
  border-radius: 2%;
  margin-left: 17%;
  border-style: inherit;
}

.can-name {
  height: 22px; 
  overflow: hidden;
  resize: none; 
  margin-top: 4%; 
  background-color: #f5f5f5; 
  margin-bottom: 2%; 
  margin-left: 1%; 
  border: none

}

.main {
  flex: 1;
  box-sizing: border-box;
  margin-top: 0px;
  margin-left: 0px;
}

.notes-text {
 /* background-color: rgb(225, 176, 107); */
  background-color: #f5f5f5;
  min-height: 100px;
}

.noResultsMessage {
  width: 75vw;
  margin-top: 8vh;
  text-align: center;
  position: absolute;
}

.navbar {
  background-color: #E08537;
}

.query-input {
  height: 60px;
  position: fixed;
  bottom: 5vh;
  width: 80vw;
}

.query-text {
  background-color: var(--orange);
}

.response-text {
  background-color: var(--white);
}

.searchbar {
  background-color:  #e49d60;
}

.separator {
  border-top: 3px dashed var(--white);
}

.side {
  box-sizing: border-box;
  flex: inherit;
  overflow-x: hidden
}

.signInBtn {
  background-color: #F18544;
  padding: 30px;
  font-size: xx-large;
  border-radius: 5px;
  border: #F18544;
  color: white;
  
  
}

.signInFlex {
  height: 100vh;
}

.locked-overlay {
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background-color: rgba(255, 255, 255, 0.7); /* semi-transparent white */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.locked-overlay-mand {
  position: absolute;
  top: 0; left: 0;
  width: 83%; height: 100%;
  background-color: rgba(255, 255, 255, 0.7); /* semi-transparent white */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.locked-block {
  position: relative;
}
.locked-block:hover {
  cursor: not-allowed;
}

.locked-container {
 position: relative;
}
.locked-container:hover .locked-overlay {
  opacity: 0;
  pointer-events: none;

}


.lock-icon {
  font-size: 2rem;
  color: #555;
}

.locked-add-question {
  opacity: 0.8;
  background-color: rgba(255, 255, 255, 0.7);
  position: relative;
  border: rgb(92, 85, 85) !important
}
.locked-add-question:hover .locked-overlay-mand {
  cursor: pointer;

}
.uploadBtn {
  background-color: #c9c0c0;
  border-block-color: #c9c0c0;
  border:#c9c0c0;
  border-style: solid;
  color:white;
  width: 115%;
  height: 38%
  
}
.newResponse{
  border-radius: 0px;
  background-color: white;
  width: 97%;
  overflow: hidden;
  border-style: unset;
  margin-left: 3%;
  font-family: "Roboto Slab";
  color:#887E7E 
}


.blackBoxes{ /*Courtesy of G*/
  border-radius: 10px;
  background-color: black; 
  width: 20% !important;
  margin-right: 3%;
  font-size:30px;
  text-align:center;
  height:71%;
}

.sampleUser{
  color:white;
  border-radius: 6px;
  background-color: #4CABAB;
  font-size:30px;
  height:15%;
  font-size: 12px;
  margin-left: 10%;
  border:#4CABAB;
  font-weight: bold;
}

.candidateSettings{
  margin-left: 10%;
  font-size: 12px;
  color:black;
  margin-bottom: 5%;
}
.color {
  --bs-table-striped-bg: red !important;
}




/*Overriden Classes*/

.dropdown-toggle:after{
  margin-left: 100% !important;
  position: fixed;
}
#dash .css-dip3t8 {
 
  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
  background-color: #E08537 !important ;
  height: 100%;
}

.css-dip3t8 {
 
  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
  background-color: #ffffff !important ;
  height: 100%;
}

.ps-sidebar-container {
  background-color: white;
}

.page-link{
  color: #E08537 !important ;
}
.active>.page-link, .page-link.active {
  background-color: #E08537 !important ;
  color: white !important;
  border: #E08537 !important;
}

.display-button-container {
  position: fixed;
  display: flex;
  gap: 10px;
  left: 50%;
  z-index: 999;
  transform: translateX(-50%);
  bottom: 20px;
}

td {
  max-width: 300px; 
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  z-index: 1;
}

 #choosePresetModal.modal-dialog{
  max-width: 750px !important;
}

#search::-webkit-input-placeholder { /* WebKit, Blink, Edge */
  color: white !important;
}
#search:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
 color: white !important;
 opacity:  1;
}
#search::-moz-placeholder { /* Mozilla Firefox 19+ */
 color: white !important;
 opacity:  1;
}
#search:-ms-input-placeholder { /* Internet Explorer 10-11 */
 color:white !important;
}
#search::-ms-input-placeholder { /* Microsoft Edge */
 color:white !important;
}

#search::placeholder { /* Most modern browsers support this now. */
 color:white !important;
}