import React from 'react';
import { IconProps } from './types';

export const LockIcon: React.FC<IconProps> = ({size=18, width = "150px", ...rest }) => {
    return (
<svg viewBox="0 0 512 512" version="1.1" width={width} xmlns="http://www.w3.org/2000/svg" fill="#000000" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>lock</title> <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="Combined-Shape" fill="#000000" transform="translate(85.333333, 42.666667)"> <path d="M170.666667,3.55271368e-14 C241.156267,3.55271368e-14 298.666667,57.6949333 298.666667,128 L298.666667,213.333333 L341.333333,213.333333 L341.333333,426.666667 L1.42108547e-14,426.666667 L1.42108547e-14,213.333333 L42.6666667,213.333333 L42.6666667,128 C42.6666667,57.6949333 100.176853,3.55271368e-14 170.666667,3.55271368e-14 Z M298.666667,256 L42.6666667,256 L42.6666667,384 L298.666667,384 L298.666667,256 Z M170.666667,42.6666667 C123.33568,42.6666667 85.3333333,81.6625067 85.3333333,128 L85.3333333,128 L85.3333333,213.333333 L256,213.333333 L256,128 C256,81.6625067 217.9968,42.6666667 170.666667,42.6666667 Z"> </path> </g> </g> </g></svg>)};