import React from 'react';
import { IconProps } from './types';

export const MeatBall: React.FC<IconProps> = ({size=18, ...rest }) => {
    return (
<svg viewBox="0 0 32 32" height="30px" width="30px" xmlns="http://www.w3.org/2000/svg" fill="none"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill="#C9C0C0" d="M16 10a2 2 0 100-4 2 2 0 000 4zM16 18a2 2 0 100-4 2 2 0 000 4zM16 26a2 2 0 100-4 2 2 0 000 4z"></path> </g></svg>
)};