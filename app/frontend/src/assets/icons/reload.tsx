import React from 'react';
import { IconProps } from './types';
export const ReloadIcon: React.FC<IconProps> = ({ size = 18, width = "150px", ...rest }) => {
    return (
        <svg viewBox="0 0 16 16" 
        width={width}
        version="1.1" id="svg6190" fill="#000000">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier"> <metadata id="metadata6196">  
        </metadata> <g transform="translate(5.025 -1037.1)" id="layer1"> 
        <path
        style={{
            backgroundColor:'#E08537', 
            opacity: 1,
            vectorEffect: "none",
            fill: "#373737",
            fillOpacity: 1,
            stroke: "none",
            strokeWidth: 4,
            strokeLinecap: "square",
            strokeLinejoin: "round",
            strokeMiterlimit: 4,
            strokeDasharray: "none",
            strokeDashoffset: 3.2,
            strokeOpacity: 0.55063291,
        }}
        d="m7.924 1040.15-1.414 1.414-1.414 1.414 1.414 1.414 1.117-1.117a5 5 0 0 1-1.117 5.36 5 5 0 0 1-5.357 1.115l-1.505 1.505a7 7 0 0 0 8.276-1.206 7 7 0 0 0 0-9.9zm-1.622-1.206a7 7 0 0 0-8.277 1.206 7 7 0 0 0 0 9.9l1.414-1.415 1.414-1.414-1.414-1.414-1.117 1.117a5 5 0 0 1 1.117-5.36 5 5 0 0 1 5.357-1.115z"
        />
        </g> </g>
        
        </svg>
    );
};