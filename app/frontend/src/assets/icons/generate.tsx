import React from 'react';
import { IconProps } from './types';

export const GenerateIcon: React.FC<IconProps> = ({size=18, color, ...rest }) => {
    return (
        <svg fill={color} version="1.1" viewBox="144 144 512 512" width={'70%'}
        height={'70%'} xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="m585.29 644.87h-370.58v-489.74h231.3l139.28 126.03zm-318.07-281.95c0.003906 3.9102 1.5586 7.6602 4.3242 10.426 2.7695 2.7656 6.5195 4.3203 10.43 4.3242h140.51c5.2773 0 10.152-2.8164 12.789-7.3828 2.6367-4.5703 2.6367-10.199 0-14.77-2.6367-4.5703-7.5117-7.3828-12.789-7.3828h-140.51c-3.9102-0.003906-7.6641 1.5508-10.434 4.3164-2.7695 2.7656-4.3203 6.5195-4.3203 10.434zm14.758 57.172-0.003907-0.003907c-3.9609-0.074219-7.7891 1.4453-10.617 4.2188-2.832 2.7773-4.4258 6.5742-4.4258 10.535 0 3.9648 1.5938 7.7617 4.4258 10.535 2.8281 2.7773 6.6562 4.2969 10.617 4.2188h214.68c5.207-0.10156 9.9727-2.9375 12.547-7.4648 2.5742-4.5234 2.5742-10.074 0-14.602-2.5742-4.5234-7.3398-7.3594-12.547-7.4609zm229.41 86.703c0.003907-3.9141-1.5469-7.6719-4.3164-10.438-2.7695-2.7695-6.5234-4.3242-10.438-4.3242h-214.66c-3.9609-0.078125-7.7891 1.4414-10.617 4.2188-2.832 2.7734-4.4258 6.5703-4.4258 10.535s1.5938 7.7617 4.4258 10.535c2.8281 2.7734 6.6562 4.2969 10.617 4.2188h214.68c3.9102-0.003906 7.6562-1.5586 10.422-4.3242 2.7617-2.7656 4.3164-6.5117 4.3203-10.422zm0 71.91c0-3.9141-1.5547-7.668-4.3203-10.434-2.7695-2.7695-6.5195-4.3242-10.434-4.3281h-214.66c-5.2031 0.10156-9.9688 2.9375-12.543 7.4609-2.5742 4.5273-2.5742 10.07 0 14.598 2.5742 4.5234 7.3398 7.3594 12.543 7.4609h214.68c3.9102-0.003906 7.6602-1.5586 10.426-4.3281 2.7656-2.7656 4.3164-6.5156 4.3164-10.43z" fill-rule="evenodd"></path> </g></svg>
  )};

  // #E08537