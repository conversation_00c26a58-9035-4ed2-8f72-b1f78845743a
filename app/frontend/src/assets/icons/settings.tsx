import React from 'react';
import { IconProps } from './types';

export const SettingsIcon: React.FC<IconProps> = ({size=18, color, ...rest }) => {
    return (

<svg version="1.1" viewBox="144 144 512 512"  width={'70%'}
            height={'70%'} xmlns="http://www.w3.org/2000/svg" fill={color}><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <clipPath id="a"> <path d="m150 148.09h500v503.81h-500z"></path> </clipPath> </defs> <g clip-path="url(#a)"> <path d="m609.15 412.71v-25.426l35.789-40.363v0.003906c2.6875-3.0391 4.3555-6.8477 4.7656-10.883 0.41016-4.0352-0.45703-8.0977-2.4805-11.617l-58.133-100.76c-2.0352-3.5195-5.125-6.3125-8.8359-7.9766-3.7109-1.668-7.8516-2.1211-11.836-1.3047l-52.824 10.793-22.031-12.711-17.051-51.156h-0.003907c-1.2891-3.8516-3.7617-7.2031-7.0625-9.5742-3.2969-2.3711-7.2617-3.6445-11.324-3.6406h-116.27c-4.0664 0-8.0312 1.2773-11.332 3.6562-3.3008 2.3789-5.7695 5.7383-7.0547 9.5977l-17.051 51.156-22.016 12.711-52.84-10.793c-3.9766-0.79688-8.1055-0.33594-11.805 1.3203-3.7031 1.6562-6.7969 4.4258-8.8516 7.9219l-58.133 100.76c-2.0312 3.5234-2.9023 7.5938-2.4922 11.637 0.41016 4.0469 2.082 7.8594 4.7773 10.898l35.809 40.324v25.422l-35.809 40.367c-2.6875 3.0391-4.3555 6.8438-4.7656 10.879-0.41016 4.0352 0.45703 8.0977 2.4805 11.617l58.133 100.76c2.0352 3.5156 5.125 6.3047 8.832 7.9727 3.7031 1.6641 7.8398 2.125 11.824 1.3086l52.844-10.793 22.012 12.711 17.051 51.156c1.293 3.8516 3.7617 7.2031 7.0625 9.5742 3.3008 2.3711 7.2617 3.6445 11.324 3.6406h116.27c4.0664 0 8.0352-1.2773 11.336-3.6562s5.7656-5.7383 7.0508-9.5977l17.051-51.156 22.031-12.711 52.824 10.793h0.003907c3.9844 0.82031 8.125 0.36328 11.836-1.3008 3.7109-1.668 6.8008-4.457 8.8359-7.9805l58.133-100.76c2.0312-3.5234 2.9023-7.5938 2.4922-11.637s-2.082-7.8555-4.7773-10.898zm-209.16 64.797c-20.555 0-40.27-8.168-54.809-22.703-14.535-14.535-22.699-34.25-22.699-54.805 0-20.559 8.1641-40.273 22.699-54.809 14.539-14.535 34.254-22.703 54.809-22.703 20.559 0 40.273 8.168 54.809 22.703s22.699 34.25 22.699 54.809c0 20.555-8.1641 40.27-22.699 54.805-14.535 14.535-34.25 22.703-54.809 22.703z"></path> </g> </g>

</svg>

    )};