import React from 'react';
import { IconProps } from './types';

export const DDArrow: React.FC<IconProps> = ({size=18, ...rest }) => {
    return (
        <svg viewBox="0 0 24 24" width="28px" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M7 10L12 15L17 10" stroke="#FFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
  )};






// 