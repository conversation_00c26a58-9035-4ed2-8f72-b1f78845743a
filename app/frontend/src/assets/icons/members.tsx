import React from 'react';
import { IconProps } from './types';

export const MembersIcon: React.FC<IconProps> = ({size=18, color, ...rest }) => {
    return (



<svg fill={color} version="1.1" viewBox="144 144 512 512"  width={'88%'}
            height={'88%'} xmlns="http://www.w3.org/2000/svg" stroke="#E08537"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="m571.77 442.9c-17.102-0.023438-33.516 6.7539-45.621 18.836s-18.914 28.48-18.93 45.582c-0.011719 17.105 6.7695 33.512 18.855 45.613 12.086 12.102 28.488 18.902 45.59 18.91 17.105 0.007812 33.512-6.7812 45.605-18.875 12.098-12.09 18.895-28.492 18.895-45.598 0-17.082-6.7812-33.469-18.855-45.559-12.074-12.09-28.453-18.891-45.539-18.91zm21.492 75.176h-10.785v10.707c0.25781 3.0078-0.75781 5.9883-2.8008 8.2148-2.043 2.2227-4.9258 3.4883-7.9453 3.4883-3.0195 0-5.9023-1.2656-7.9453-3.4883-2.043-2.2266-3.0586-5.207-2.8008-8.2148v-10.707h-10.703c-5.582-0.47656-9.8672-5.1445-9.8672-10.746 0-5.5977 4.2852-10.266 9.8672-10.742h10.707l-0.003906-10.707c0.48047-5.5781 5.1484-9.8633 10.746-9.8633 5.5977 0 10.266 4.2852 10.746 9.8633v10.707h10.785c5.5781 0.47656 9.8633 5.1445 9.8633 10.742 0 5.6016-4.2852 10.27-9.8633 10.746z"></path> <path d="m444.16 370.4-33.457-13.305-32.195 21.41-32.195-21.41-33.379 13.305c-5.9258 2.375-11.016 6.4531-14.617 11.723-3.6016 5.2734-5.5547 11.492-5.6133 17.879v96.59h171.69v-96.59c-0.078125-6.3789-2.0391-12.594-5.6406-17.859-3.5977-5.2656-8.6758-9.3516-14.59-11.742z"></path> <path d="m432.2 281.92c0 29.652-24.035 53.688-53.688 53.688-29.652 0-53.688-24.035-53.688-53.688 0-29.652 24.035-53.688 53.688-53.688 29.652 0 53.688 24.035 53.688 53.688"></path> <path d="m239.02 400-32.273-21.492-25.113 12.594c-5.3438 2.6641-9.8359 6.7617-12.98 11.84-3.1406 5.0742-4.8086 10.926-4.8125 16.895v76.754h107.38v-96.59c-0.050782-6.8047 1.2617-13.551 3.8594-19.84l-3.8555-1.9688z"></path> <path d="m281.92 314.11c0 23.695-19.207 42.902-42.902 42.902s-42.902-19.207-42.902-42.902c0-23.691 19.207-42.902 42.902-42.902s42.902 19.211 42.902 42.902"></path> <path d="m571.77 421.49c7.2227 0.003907 14.414 0.90234 21.414 2.6758v-4.3281c-0.003907-5.9688-1.668-11.82-4.8125-16.895-3.1406-5.0781-7.6367-9.1758-12.98-11.84l-25.113-12.594-32.195 21.492-32.195-21.492-3.7773 1.8125c2.4492 6.2734 3.7305 12.941 3.7773 19.68v96.59h0.70703c2.6758-20.723 12.789-39.762 28.457-53.578 15.672-13.816 35.828-21.465 56.719-21.523z"></path> <path d="m560.98 314.11c0 23.695-19.207 42.902-42.902 42.902s-42.902-19.207-42.902-42.902c0-23.691 19.207-42.902 42.902-42.902s42.902 19.211 42.902 42.902"></path> </g> </g>

</svg>

    )};