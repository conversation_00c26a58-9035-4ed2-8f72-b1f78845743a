/**
 * API service for making HTTP requests to the backend.
 */
import { API_BASE_URL, API_HEADERS, API_ENDPOINTS } from '../constants/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.headers = API_HEADERS;
  }

  /**
   * Make a generic API request
   * @param {string} endpoint - The API endpoint
   * @param {string} method - HTTP method (GET, POST, etc.)
   * @param {Object} data - Request data
   * @param {Object} customHeaders - Custom headers
   * @returns {Promise} - API response
   */
  async request(endpoint, method = 'GET', data = null, customHeaders = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      method,
      headers: {
        ...this.headers,
        ...customHeaders,
      },
    };

    if (data) {
      if (data instanceof FormData) {
        // Remove Content-Type header for FormData (browser will set it)
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.body = JSON.stringify(data);
      }
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Candidate API methods
  async getAllCandidates() {
    return this.request(API_ENDPOINTS.LEGACY.GET_ALL);
  }

  async getCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.GET_CANDIDATE, 'POST', candidateData);
  }

  async addCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.ADD_CANDIDATE, 'POST', candidateData);
  }

  async updateCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.UPDATE_CANDIDATE, 'POST', candidateData);
  }

  async deleteCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.DELETE_CANDIDATE, 'POST', candidateData);
  }

  // Interview API methods
  async generateQuestions(formData) {
    return this.request(API_ENDPOINTS.LEGACY.GENERATE_QUESTIONS, 'POST', formData);
  }

  async regenerateTechnicalQuestions(data) {
    return this.request(API_ENDPOINTS.LEGACY.REGENERATE_TECHNICAL, 'POST', data);
  }

  async regenerateBehavioralQuestions(data) {
    return this.request(API_ENDPOINTS.LEGACY.REGENERATE_BEHAVIORAL, 'POST', data);
  }

  // Job Description API methods
  async getAllJobDescriptions() {
    return this.request(API_ENDPOINTS.LEGACY.GET_ALL_JD);
  }

  async getJobDescription(jdData) {
    return this.request(API_ENDPOINTS.LEGACY.GET_JD, 'POST', jdData);
  }

  async addJobDescription(jdData) {
    return this.request(API_ENDPOINTS.LEGACY.ADD_JD, 'POST', jdData);
  }

  async updateJobDescription(jdData) {
    return this.request(API_ENDPOINTS.LEGACY.UPDATE_JD, 'POST', jdData);
  }

  async deleteJobDescription(jdData) {
    return this.request(API_ENDPOINTS.LEGACY.DELETE_JD, 'POST', jdData);
  }

  // Archive API methods
  async getAllArchivedCandidates() {
    return this.request(API_ENDPOINTS.LEGACY.GET_ALL_ARCHIVED);
  }

  async getArchivedCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.GET_ARCHIVED_CANDIDATE, 'POST', candidateData);
  }

  async addArchivedCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.ADD_ARCHIVED_CANDIDATE, 'POST', candidateData);
  }

  async deleteArchivedCandidate(candidateData) {
    return this.request(API_ENDPOINTS.LEGACY.DELETE_ARCHIVED_CANDIDATE, 'POST', candidateData);
  }
}

// Export a singleton instance
export default new ApiService();
