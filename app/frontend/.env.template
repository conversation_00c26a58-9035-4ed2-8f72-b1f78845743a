# Frontend Environment Variables

# Backend API Configuration
# This should match the custom header key from nginx.conf
REACT_APP_BACKEND_API=your_backend_api_key_here

# Backend API Base URL
REACT_APP_API_BASE_URL=http://localhost:8002

# Application Environment
REACT_APP_ENV=development

# Azure AD Configuration
# Get these values from your Azure AD app registration
REACT_APP_AZURE_CLIENT_ID=your_azure_client_id_here
REACT_APP_AZURE_AUTHORITY=https://login.microsoftonline.com/your_tenant_domain_here

# Development URLs
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_POST_LOGOUT_REDIRECT_URI=http://localhost:3000

# Production URLs (uncomment for production)
# REACT_APP_REDIRECT_URI=https://interviews.creospan.com
# REACT_APP_POST_LOGOUT_REDIRECT_URI=https://interviews.creospan.com

# File Upload Configuration
REACT_APP_MAX_FILE_SIZE=5368709120

# Debug Configuration
REACT_APP_DEBUG=true
GENERATE_SOURCEMAP=false
