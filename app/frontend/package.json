{"name": "react-front-end", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.3.0", "@azure/msal-react": "^2.0.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.3.2", "docx": "^8.2.2", "dotenv": "^16.3.1", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-dom": "^18.2.0", "react-router-dom": "^5.3.3", "react-scripts": "^5.0.1", "react-pro-sidebar": "^1.1.0", "react-textarea-autosize": "^8.5.3", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "sass": "^1.32.0"}, "devDependencies": {"typescript": "^4.3.5", "eslint": "^7.32.0", "@typescript-eslint/parser": "^4.33.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-react-app": "^6.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "extends": ["react-app", "react-app/jest", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}